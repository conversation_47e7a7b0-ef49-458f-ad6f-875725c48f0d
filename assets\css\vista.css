/*!
online design
*/
@font-face {
  font-family: 'nbd-vista';
  src: url("fonts/nbd-vista.eot?u6xccv");
  src: url("fonts/nbd-vista.eot?u6xccv#iefix") format("embedded-opentype"), url("fonts/nbd-vista.ttf?u6xccv") format("truetype"), url("fonts/nbd-vista.woff?u6xccv") format("woff"), url("fonts/nbd-vista.svg?u6xccv#nbd-vista") format("svg");
  font-weight: normal;
  font-style: normal; }

[class^="nbd-icon-vista-"], [class*=" nbd-icon-vista-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'nbd-vista' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.nbd-icon-vista-sent-to-backward:before {
  content: "\E93C"; }

.nbd-icon-vista-bring-forward:before {
  content: "\E93D"; }

.nbd-icon-vista-bring-to-front:before {
  content: "\E93E"; }

.nbd-icon-vista-headline:before {
  content: "\E93B"; }

.nbd-icon-vista-opacity:before {
  content: "\E939"; }

.nbd-icon-vista-apps:before {
  content: "\E93A"; }

.nbd-icon-vista-center:before {
  content: "\E936"; }

.nbd-icon-vista-top-left:before {
  content: "\E937"; }

.nbd-icon-vista-fullscreen:before {
  content: "\E935"; }

.nbd-icon-vista-remove:before {
  content: "\E933"; }

.nbd-icon-vista-add-black:before {
  content: "\E934"; }

.nbd-icon-vista-search:before {
  content: "\E932"; }

.nbd-icon-vista-arrow-upward:before {
  content: "\E931"; }

.nbd-icon-vista-add:before {
  content: "\E900"; }

.nbd-icon-vista-align-center:before {
  content: "\E901"; }

.nbd-icon-vista-align-justify:before {
  content: "\E902"; }

.nbd-icon-vista-align-left:before {
  content: "\E903"; }

.nbd-icon-vista-align-right:before {
  content: "\E904"; }

.nbd-icon-vista-arrow-drop-down:before {
  content: "\E905"; }

.nbd-icon-vista-arrows:before {
  content: "\E906";
  color: #3f4652; }

.nbd-icon-vista-attachment:before {
  content: "\E907"; }

.nbd-icon-vista-bold:before {
  content: "\E908"; }

.nbd-icon-vista-camera-alt:before {
  content: "\E909"; }

.nbd-icon-vista-clear:before {
  content: "\E90A"; }

.nbd-icon-vista-cloud-upload:before {
  content: "\E90B"; }

.nbd-icon-vista-copy:before {
  content: "\E90C"; }

.nbd-icon-vista-delete:before {
  content: "\E90D"; }

.nbd-icon-vista-diamond:before {
  content: "\E90E"; }

.nbd-icon-vista-dis-horizontal:before {
  content: "\E90F"; }

.nbd-icon-vista-dis-vertical:before {
  content: "\E910"; }

.nbd-icon-vista-done:before {
  content: "\E911"; }

.nbd-icon-vista-drawing:before {
  content: "\E912"; }

.nbd-icon-vista-dropbox-logo:before {
  content: "\E913"; }

.nbd-icon-vista-expand-more:before {
  content: "\E914"; }

.nbd-icon-vista-facebook-logo:before {
  content: "\E915"; }

.nbd-icon-vista-file-upload:before {
  content: "\E916"; }

.nbd-icon-vista-group-work:before {
  content: "\E917"; }

.nbd-icon-vista-image:before {
  content: "\E918"; }

.nbd-icon-vista-instagram-logo:before {
  content: "\E919"; }

.nbd-icon-vista-italic:before {
  content: "\E91A"; }

.nbd-icon-vista-line:before {
  content: "\E91B"; }

.nbd-icon-vista-more:before {
  content: "\E91C"; }

.nbd-icon-vista-navigate-next:before {
  content: "\E91D"; }

.nbd-icon-vista-pixabay:before {
  content: "\E91E"; }

.nbd-icon-vista-proof:before {
  content: "\E91F"; }

.nbd-icon-vista-redo:before {
  content: "\E920"; }

.nbd-icon-vista-reflect-horizontal:before {
  content: "\E921"; }

.nbd-icon-vista-reflect-vertical:before {
  content: "\E922"; }

.nbd-icon-vista-refresh:before {
  content: "\E923"; }

.nbd-icon-vista-replace-image:before {
  content: "\E924"; }

.nbd-icon-vista-rotate-right:before {
  content: "\E925"; }

.nbd-icon-vista-save:before {
  content: "\E926"; }

.nbd-icon-vista-shapes:before {
  content: "\E927"; }

.nbd-icon-vista-text:before {
  content: "\E928"; }

.nbd-icon-vista-undo:before {
  content: "\E929"; }

.nbd-icon-vista-ungroup:before {
  content: "\E92A"; }

.nbd-icon-vista-vertical-align-center:before {
  content: "\E92B"; }

.nbd-icon-vista-vertical-align-top:before {
  content: "\E92C"; }

.nbd-icon-vista-warning:before {
  content: "\E92D"; }

.nbd-icon-vista-webcam:before {
  content: "\E92E"; }

.nbd-icon-vista-zoom-in:before {
  content: "\E92F"; }

.nbd-icon-vista-zoom-out:before {
  content: "\E930"; }

.nbd-icon-vista-cog:before {
  content: "\F013"; }

.nbd-icon-vista-gear:before {
  content: "\F013"; }

.nbd-icon-vista-lock:before {
  content: "\F023"; }

.nbd-icon-vista-expand:before {
  content: "\F065"; }

.nbd-icon-vista-arrows-v:before {
  content: "\F07D"; }

.nbd-icon-vista-arrows-h:before {
  content: "\F07E"; }

.nbd-icon-vista-qrcode:before {
  content: "\E938"; }

.nbd-designer #primary, .nbd-designer.woocommerce div.product {
  overflow: hidden; }

.nbd-mode-vista {
  /* HTML5 display-role reset for older browsers */
  /* HTML5 hidden-attribute fix for newer browsers */
  /* perfect-scrollbar v0.8.1 */
  /*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 Daniel Eden
*/
  /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
  /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
  /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
  /* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
  /***
Spectrum Colorpicker v1.8.0
https://github.com/bgrins/spectrum
Author: Brian Grinstead
License: MIT
***/
  /* Fix for * { box-sizing: border-box; } */
  /* http://ansciath.tumblr.com/post/**********/css-aspect-ratio */
  /* Don't allow text selection */
  /* Gradients for hue, saturation and value instead of images.  Not pretty... but it works */
  /* IE filters do not support multiple color stops.
   Generate 6 divs, line them up, and do two color gradients for each.
   Yes, really.
 */
  /* Clearfix hack */
  /* Mobile devices, make hue slider bigger so it is easier to slide */
  /*
Theme authors:
Here are the basic themeable display options (colors, fonts, global widths).
See http://bgrins.github.io/spectrum/themes/ for instructions.
*/
  /* Input */
  /* Palettes */
  /* Initial */
  /* Buttons */
  /* Replacer (the little preview div that shows up instead of the <input>) */
  /* Buttons: http://hellohappy.org/css3-buttons/ */
  /*! jQuery UI - v1.10.4 - 2014-01-17
* http://jqueryui.com
* Includes: jquery.ui.core.css, jquery.ui.accordion.css, jquery.ui.autocomplete.css, jquery.ui.button.css, jquery.ui.datepicker.css, jquery.ui.dialog.css, jquery.ui.menu.css, jquery.ui.progressbar.css, jquery.ui.resizable.css, jquery.ui.selectable.css, jquery.ui.slider.css, jquery.ui.spinner.css, jquery.ui.tabs.css, jquery.ui.tooltip.css, jquery.ui.theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?ffDefault=Verdana%2CArial%2Csans-serif&fwDefault=normal&fsDefault=1.1em&cornerRadius=4px&bgColorHeader=cccccc&bgTextureHeader=highlight_soft&bgImgOpacityHeader=75&borderColorHeader=aaaaaa&fcHeader=222222&iconColorHeader=222222&bgColorContent=ffffff&bgTextureContent=flat&bgImgOpacityContent=75&borderColorContent=aaaaaa&fcContent=222222&iconColorContent=222222&bgColorDefault=e6e6e6&bgTextureDefault=glass&bgImgOpacityDefault=75&borderColorDefault=d3d3d3&fcDefault=555555&iconColorDefault=888888&bgColorHover=dadada&bgTextureHover=glass&bgImgOpacityHover=75&borderColorHover=999999&fcHover=212121&iconColorHover=454545&bgColorActive=ffffff&bgTextureActive=glass&bgImgOpacityActive=65&borderColorActive=aaaaaa&fcActive=212121&iconColorActive=454545&bgColorHighlight=fbf9ee&bgTextureHighlight=glass&bgImgOpacityHighlight=55&borderColorHighlight=fcefa1&fcHighlight=363636&iconColorHighlight=2e83ff&bgColorError=fef1ec&bgTextureError=glass&bgImgOpacityError=95&borderColorError=cd0a0a&fcError=cd0a0a&iconColorError=cd0a0a&bgColorOverlay=aaaaaa&bgTextureOverlay=flat&bgImgOpacityOverlay=0&opacityOverlay=30&bgColorShadow=aaaaaa&bgTextureShadow=flat&bgImgOpacityShadow=0&opacityShadow=30&thicknessShadow=8px&offsetTopShadow=-8px&offsetLeftShadow=-8px&cornerRadiusShadow=8px
* Copyright 2014 jQuery Foundation and other contributors; Licensed MIT */
  /* MAD-RIPPLE EFFECT */ }
  .nbd-mode-vista.nbd-onloading {
    pointer-events: none; }
  .nbd-mode-vista .nbd-vista {
    height: 580px;
    position: relative;
    z-index: 1; }
  .nbd-mode-vista html, .nbd-mode-vista body, .nbd-mode-vista div, .nbd-mode-vista span, .nbd-mode-vista applet, .nbd-mode-vista object, .nbd-mode-vista iframe,
  .nbd-mode-vista h1, .nbd-mode-vista h2, .nbd-mode-vista h3, .nbd-mode-vista h4, .nbd-mode-vista h5, .nbd-mode-vista h6, .nbd-mode-vista p, .nbd-mode-vista blockquote, .nbd-mode-vista pre,
  .nbd-mode-vista a, .nbd-mode-vista abbr, .nbd-mode-vista acronym, .nbd-mode-vista address, .nbd-mode-vista big, .nbd-mode-vista cite, .nbd-mode-vista code,
  .nbd-mode-vista del, .nbd-mode-vista dfn, .nbd-mode-vista em, .nbd-mode-vista img, .nbd-mode-vista ins, .nbd-mode-vista kbd, .nbd-mode-vista q, .nbd-mode-vista s, .nbd-mode-vista samp,
  .nbd-mode-vista small, .nbd-mode-vista strike, .nbd-mode-vista strong, .nbd-mode-vista sub, .nbd-mode-vista sup, .nbd-mode-vista tt, .nbd-mode-vista var,
  .nbd-mode-vista b, .nbd-mode-vista u, .nbd-mode-vista i, .nbd-mode-vista center,
  .nbd-mode-vista dl, .nbd-mode-vista dt, .nbd-mode-vista dd, .nbd-mode-vista ol, .nbd-mode-vista ul, .nbd-mode-vista li,
  .nbd-mode-vista fieldset, .nbd-mode-vista form, .nbd-mode-vista label, .nbd-mode-vista legend,
  .nbd-mode-vista table, .nbd-mode-vista caption, .nbd-mode-vista tbody, .nbd-mode-vista tfoot, .nbd-mode-vista thead, .nbd-mode-vista tr, .nbd-mode-vista th, .nbd-mode-vista td,
  .nbd-mode-vista article, .nbd-mode-vista aside, .nbd-mode-vista canvas, .nbd-mode-vista details, .nbd-mode-vista embed,
  .nbd-mode-vista figure, .nbd-mode-vista figcaption, .nbd-mode-vista footer, .nbd-mode-vista header, .nbd-mode-vista hgroup,
  .nbd-mode-vista main, .nbd-mode-vista menu, .nbd-mode-vista nav, .nbd-mode-vista output, .nbd-mode-vista ruby, .nbd-mode-vista section, .nbd-mode-vista summary,
  .nbd-mode-vista time, .nbd-mode-vista mark, .nbd-mode-vista audio, .nbd-mode-vista video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline; }
  .nbd-mode-vista article, .nbd-mode-vista aside, .nbd-mode-vista details, .nbd-mode-vista figcaption, .nbd-mode-vista figure,
  .nbd-mode-vista footer, .nbd-mode-vista header, .nbd-mode-vista hgroup, .nbd-mode-vista main, .nbd-mode-vista menu, .nbd-mode-vista nav, .nbd-mode-vista section {
    display: block; }
  .nbd-mode-vista *[hidden] {
    display: none; }
  .nbd-mode-vista * {
    box-sizing: border-box; }
  .nbd-mode-vista body {
    line-height: 1; }
  .nbd-mode-vista ol, .nbd-mode-vista ul {
    list-style: none; }
  .nbd-mode-vista blockquote, .nbd-mode-vista q {
    quotes: none; }
  .nbd-mode-vista blockquote:before, .nbd-mode-vista blockquote:after,
  .nbd-mode-vista q:before, .nbd-mode-vista q:after {
    content: '';
    content: none; }
  .nbd-mode-vista table {
    border-collapse: collapse;
    border-spacing: 0; }
  .nbd-mode-vista .ps {
    -ms-touch-action: auto;
    touch-action: auto;
    overflow: hidden !important;
    -ms-overflow-style: none; }
  @supports (-ms-overflow-style: none) {
    .nbd-mode-vista .ps {
      overflow: auto !important; } }
  @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .nbd-mode-vista .ps {
      overflow: auto !important; } }
  .nbd-mode-vista .ps.ps--active-x > .ps__scrollbar-x-rail,
  .nbd-mode-vista .ps.ps--active-y > .ps__scrollbar-y-rail {
    display: block;
    background-color: transparent; }
  .nbd-mode-vista .ps.ps--in-scrolling.ps--x > .ps__scrollbar-x-rail {
    background-color: #eee;
    opacity: 0.9; }
  .nbd-mode-vista .ps.ps--in-scrolling.ps--x > .ps__scrollbar-x-rail > .ps__scrollbar-x {
    background-color: #999;
    height: 9px; }
  .nbd-mode-vista .ps.ps--in-scrolling.ps--y > .ps__scrollbar-y-rail {
    background-color: #eee;
    opacity: 0.9; }
  .nbd-mode-vista .ps.ps--in-scrolling.ps--y > .ps__scrollbar-y-rail > .ps__scrollbar-y {
    background-color: #999;
    width: 9px; }
  .nbd-mode-vista .ps > .ps__scrollbar-x-rail {
    display: none;
    position: absolute;
    /* please don't change 'position' */
    opacity: 0;
    -webkit-transition: background-color .2s linear, opacity .2s linear;
    -o-transition: background-color .2s linear, opacity .2s linear;
    -moz-transition: background-color .2s linear, opacity .2s linear;
    transition: background-color .2s linear, opacity .2s linear;
    bottom: 0px;
    /* there must be 'bottom' for ps__scrollbar-x-rail */
    height: 5px; }
  .nbd-mode-vista .ps > .ps__scrollbar-x-rail > .ps__scrollbar-x {
    position: absolute;
    /* please don't change 'position' */
    background-color: #aaa;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, -webkit-border-radius .2s ease-in-out;
    transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, -webkit-border-radius .2s ease-in-out;
    -o-transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out;
    -moz-transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out, -moz-border-radius .2s ease-in-out;
    transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out;
    transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out, -webkit-border-radius .2s ease-in-out, -moz-border-radius .2s ease-in-out;
    bottom: 2px;
    /* there must be 'bottom' for ps__scrollbar-x */
    height: 6px; }
  .nbd-mode-vista .ps > .ps__scrollbar-x-rail:hover > .ps__scrollbar-x, .nbd-mode-vista .ps > .ps__scrollbar-x-rail:active > .ps__scrollbar-x {
    height: 9px; }
  .nbd-mode-vista .ps > .ps__scrollbar-y-rail {
    display: none;
    position: absolute;
    /* please don't change 'position' */
    opacity: 0;
    -webkit-transition: background-color .2s linear, opacity .2s linear;
    -o-transition: background-color .2s linear, opacity .2s linear;
    -moz-transition: background-color .2s linear, opacity .2s linear;
    transition: background-color .2s linear, opacity .2s linear;
    right: 0;
    /* there must be 'right' for ps__scrollbar-y-rail */
    width: 9px; }
  .nbd-mode-vista .ps > .ps__scrollbar-y-rail > .ps__scrollbar-y {
    position: absolute;
    /* please don't change 'position' */
    background-color: #aaa;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, -webkit-border-radius .2s ease-in-out;
    transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, -webkit-border-radius .2s ease-in-out;
    -o-transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out;
    -moz-transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out, -moz-border-radius .2s ease-in-out;
    transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out;
    transition: background-color .2s linear, height .2s linear, width .2s ease-in-out, border-radius .2s ease-in-out, -webkit-border-radius .2s ease-in-out, -moz-border-radius .2s ease-in-out;
    right: 0px;
    /* there must be 'right' for ps__scrollbar-y */
    width: 5px; }
  .nbd-mode-vista .ps > .ps__scrollbar-y-rail:hover > .ps__scrollbar-y, .nbd-mode-vista .ps > .ps__scrollbar-y-rail:active > .ps__scrollbar-y {
    width: 9px; }
  .nbd-mode-vista .ps:hover.ps--in-scrolling.ps--x > .ps__scrollbar-x-rail {
    background-color: #eee;
    opacity: 0.9; }
  .nbd-mode-vista .ps:hover.ps--in-scrolling.ps--x > .ps__scrollbar-x-rail > .ps__scrollbar-x {
    background-color: #999;
    height: 9px; }
  .nbd-mode-vista .ps:hover.ps--in-scrolling.ps--y > .ps__scrollbar-y-rail {
    background-color: #eee;
    opacity: 0.9; }
  .nbd-mode-vista .ps:hover.ps--in-scrolling.ps--y > .ps__scrollbar-y-rail > .ps__scrollbar-y {
    background-color: #999;
    width: 9px; }
  .nbd-mode-vista .ps:hover > .ps__scrollbar-x-rail,
  .nbd-mode-vista .ps:hover > .ps__scrollbar-y-rail {
    opacity: 0.6; }
  .nbd-mode-vista .ps:hover > .ps__scrollbar-x-rail:hover {
    background-color: #eee;
    opacity: 0.9; }
  .nbd-mode-vista .ps:hover > .ps__scrollbar-x-rail:hover > .ps__scrollbar-x {
    background-color: #999; }
  .nbd-mode-vista .ps:hover > .ps__scrollbar-y-rail:hover {
    background-color: #eee;
    opacity: 0.9; }
  .nbd-mode-vista .ps:hover > .ps__scrollbar-y-rail:hover > .ps__scrollbar-y {
    background-color: #999; }
  .nbd-mode-vista .animated {
    animation-duration: 1s;
    animation-fill-mode: both; }
    .nbd-mode-vista .animated--infinite {
      animation-iteration-count: infinite; }

@keyframes slideInDown {
  0% {
    transform: translate3d(0, -100%, 0);
    visibility: visible; }
  100% {
    transform: translate3d(0, 0, 0); } }
  .nbd-mode-vista .slideInDown {
    animation-name: slideInDown; }

@keyframes slideInLeft {
  0% {
    transform: translate3d(-100%, 0, 0);
    visibility: visible; }
  100% {
    transform: translate3d(0, 0, 0); } }
  .nbd-mode-vista .slideInLeft {
    animation-name: slideInLeft; }

@keyframes slideInRight {
  0% {
    transform: translate3d(100%, 0, 0);
    visibility: visible; }
  100% {
    transform: translate3d(0, 0, 0); } }
  .nbd-mode-vista .slideInRight {
    animation-name: slideInRight; }

@keyframes slideInUp {
  0% {
    transform: translate3d(0, 100%, 0);
    visibility: visible; }
  100% {
    transform: translate3d(0, 0, 0); } }
  .nbd-mode-vista .slideInUp {
    animation-name: slideInUp; }

@keyframes nbSlideInDown {
  0% {
    transform: translate3d(0, 0%, 0);
    opacity: 1;
    visibility: visible; }
  100% {
    opacity: 0;
    transform: translate3d(0, 100%, 0); } }
  .nbd-mode-vista .nbSlideInDown {
    animation-name: nbSlideInDown; }

@keyframes nbSlideInUp {
  0% {
    transform: translate3d(0, 100%, 0);
    visibility: visible;
    opacity: 0; }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1; } }
  .nbd-mode-vista .nbSlideInUp {
    animation-name: nbSlideInUp; }

@keyframes nbScaleIn {
  0% {
    transform: scale(1);
    visibility: visible;
    opacity: 1; }
  100% {
    transform: scale(0.8);
    opacity: 0; } }
  .nbd-mode-vista .nbScaleIn {
    animation-name: nbScaleIn; }

@keyframes nbScaleOut {
  0% {
    transform: scale(0.8);
    visibility: visible;
    opacity: 0; }
  100% {
    transform: scale(1);
    opacity: 1; } }
  .nbd-mode-vista .nbScaleOut {
    animation-name: nbScaleOut; }

@keyframes bounce {
  0%, 20%, 53%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0); }
  80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0) scaleY(0.95); }
  40%, 43% {
    transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0) scaleY(1.1); }
  70% {
    transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0) scaleY(1.05); }
  90% {
    transform: translate3d(0, -4px, 0) scaleY(1.02); } }
  .nbd-mode-vista .bounce {
    animation-name: bounce;
    transform-origin: center bottom; }

@keyframes flash {
  0%, 50%, 100% {
    opacity: 1; }
  25%, 75% {
    opacity: 0; } }
  .nbd-mode-vista .flash {
    animation-name: flash; }

@keyframes pulse {
  0% {
    transform: scale3d(1, 1, 1); }
  50% {
    transform: scale3d(1.05, 1.05, 1.05); }
  100% {
    transform: scale3d(1, 1, 1); } }
  .nbd-mode-vista .pulse {
    animation-name: pulse;
    animation-timing-function: ease-in-out; }

@keyframes rubberBand {
  0% {
    transform: scale3d(1, 1, 1); }
  30% {
    transform: scale3d(1.25, 0.75, 1); }
  40% {
    transform: scale3d(0.75, 1.25, 1); }
  50% {
    transform: scale3d(1.15, 0.85, 1); }
  65% {
    transform: scale3d(0.95, 1.05, 1); }
  75% {
    transform: scale3d(1.05, 0.95, 1); }
  100% {
    transform: scale3d(1, 1, 1); } }
  .nbd-mode-vista .rubberBand {
    animation-name: rubberBand; }

@keyframes shake {
  0%, 100% {
    transform: translate3d(0, 0, 0); }
  10%, 30%, 50%, 70%, 90% {
    transform: translate3d(-10px, 0, 0); }
  20%, 40%, 60%, 80% {
    transform: translate3d(10px, 0, 0); } }
  .nbd-mode-vista .shake {
    animation-name: shake; }

@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg); }
  40% {
    transform: rotate3d(0, 0, 1, -10deg); }
  60% {
    transform: rotate3d(0, 0, 1, 5deg); }
  80% {
    transform: rotate3d(0, 0, 1, -5deg); }
  100% {
    transform: rotate3d(0, 0, 1, 0deg); } }
  .nbd-mode-vista .swing {
    transform-origin: top center;
    animation-name: swing; }

@keyframes tada {
  0% {
    transform: scale3d(1, 1, 1); }
  10%, 20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg); }
  30%, 50%, 70%, 90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg); }
  40%, 60%, 80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg); }
  100% {
    transform: scale3d(1, 1, 1); } }
  .nbd-mode-vista .tada {
    animation-name: tada; }

@keyframes wobble {
  0% {
    transform: none; }
  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg); }
  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg); }
  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg); }
  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg); }
  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg); }
  100% {
    transform: none; } }
  .nbd-mode-vista .wobble {
    animation-name: wobble; }

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  20% {
    transform: scale3d(1.1, 1.1, 1.1); }
  40% {
    transform: scale3d(0.9, 0.9, 0.9); }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03); }
  80% {
    transform: scale3d(0.97, 0.97, 0.97); }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1); } }
  .nbd-mode-vista .bounceIn {
    animation-name: bounceIn; }

@keyframes bounceInDown {
  0%, 60%, 75%, 90%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    transform: translate3d(0, -3000px, 0) scaleY(5); }
  60% {
    opacity: 1;
    transform: translate3d(0, 25px, 0) scaleY(0.9); }
  75% {
    transform: translate3d(0, -10px, 0) scaleY(0.95); }
  90% {
    transform: translate3d(0, 5px, 0) scaleY(0.985); }
  100% {
    transform: none; } }
  .nbd-mode-vista .bounceInDown {
    animation-name: bounceInDown; }

@keyframes bounceInLeft {
  0%, 60%, 75%, 90%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0) scaleX(3); }
  60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0) scaleX(1); }
  75% {
    transform: translate3d(-10px, 0, 0) scaleX(0.98); }
  90% {
    transform: translate3d(5px, 0, 0) scaleX(0.995); }
  100% {
    transform: none; } }
  .nbd-mode-vista .bounceInLeft {
    animation-name: bounceInLeft; }

@keyframes bounceInRight {
  0%, 60%, 75%, 90%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    transform: translate3d(3000px, 0, 0) scaleX(3); }
  60% {
    opacity: 1;
    transform: translate3d(-25px, 0, 0) scaleX(1); }
  75% {
    transform: translate3d(10px, 0, 0) scaleX(0.98); }
  90% {
    transform: translate3d(-5px, 0, 0) scaleX(0.995); }
  100% {
    transform: none; } }
  .nbd-mode-vista .bounceInRight {
    animation-name: bounceInRight; }

@keyframes bounceInUp {
  0%, 60%, 75%, 90%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1); }
  0% {
    opacity: 0;
    transform: translate3d(0, 3000px, 0) scaleY(5); }
  60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0) scaleY(0.9); }
  75% {
    transform: translate3d(0, 10px, 0) scaleY(0.95); }
  90% {
    transform: translate3d(0, -5px, 0) scaleY(0.985); }
  100% {
    transform: translate3d(0, 0, 0); } }
  .nbd-mode-vista .bounceInUp {
    animation-name: bounceInUp; }

@keyframes bounceOut {
  20% {
    transform: scale3d(0.9, 0.9, 0.9); }
  50%, 55% {
    opacity: 1;
    transform: scale3d(1.1, 1.1, 1.1); }
  100% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); } }
  .nbd-mode-vista .bounceOut {
    animation-name: bounceOut; }

@keyframes bounceOutDown {
  20% {
    transform: translate3d(0, 10px, 0) scaleY(0.985); }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, -20px, 0) scaleY(0.9); }
  100% {
    opacity: 0;
    transform: translate3d(0, 2000px, 0) scaleY(3); } }
  .nbd-mode-vista .bounceOutDown {
    animation-name: bounceOutDown; }

@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, 0, 0) scaleX(0.9); }
  100% {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0) scaleX(2); } }
  .nbd-mode-vista .bounceOutLeft {
    animation-name: bounceOutLeft; }

@keyframes bounceOutRight {
  20% {
    opacity: 1;
    transform: translate3d(-20px, 0, 0) scaleX(0.9); }
  100% {
    opacity: 0;
    transform: translate3d(2000px, 0, 0) scaleX(2); } }
  .nbd-mode-vista .bounceOutRight {
    animation-name: bounceOutRight; }

@keyframes bounceOutUp {
  20% {
    transform: translate3d(0, -10px, 0) scaleY(0.985); }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, 20px, 0) scaleY(0.9); }
  100% {
    opacity: 0;
    transform: translate3d(0, -2000px, 0) scaleY(3); } }
  .nbd-mode-vista .bounceOutUp {
    animation-name: bounceOutUp; }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
  .nbd-mode-vista .fadeIn {
    animation-name: fadeIn; }

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -100%, 0) scaleY(1.2); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInDown {
    animation-name: fadeInDown; }

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translate3d(-100%, 0, 0) scaleX(1.2); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInLeft {
    animation-name: fadeInLeft; }

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translate3d(100%, 0, 0) scaleX(1.2); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInRight {
    animation-name: fadeInRight; }

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0) scaleY(1.2); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInUp {
    animation-name: fadeInUp; }

@keyframes fadeInDownBig {
  0% {
    opacity: 0;
    transform: translate3d(0, -2000px, 0) scaleY(3); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInDownBig {
    animation-name: fadeInDownBig; }

@keyframes fadeInLeftBig {
  0% {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0) scaleX(2); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInLeftBig {
    animation-name: fadeInLeftBig; }

@keyframes fadeInRightBig {
  0% {
    opacity: 0;
    transform: translate3d(2000px, 0, 0) scaleX(2); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInRightBig {
    animation-name: fadeInRightBig; }

@keyframes fadeInUpBig {
  0% {
    opacity: 0;
    transform: translate3d(0, 2000px, 0) scaleY(3); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .fadeInUpBig {
    animation-name: fadeInUpBig; }

@keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
  .nbd-mode-vista .fadeOut {
    animation-name: fadeOut; }

@keyframes fadeOutDown {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(0, 100%, 0); } }
  .nbd-mode-vista .fadeOutDown {
    animation-name: fadeOutDown; }

@keyframes fadeOutLeft {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(-100%, 0, 0); } }
  .nbd-mode-vista .fadeOutLeft {
    animation-name: fadeOutLeft; }

@keyframes fadeOutRight {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(100%, 0, 0); } }
  .nbd-mode-vista .fadeOutRight {
    animation-name: fadeOutRight; }

@keyframes fadeOutUp {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(0, -100%, 0); } }
  .nbd-mode-vista .fadeOutUp {
    animation-name: fadeOutUp; }

@keyframes fadeOutDownBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(0, 2000px, 0); } }
  .nbd-mode-vista .fadeOutDownBig {
    animation-name: fadeOutDownBig; }

@keyframes fadeOutLeftBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0); } }
  .nbd-mode-vista .fadeOutLeftBig {
    animation-name: fadeOutLeftBig; }

@keyframes fadeOutRightBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(2000px, 0, 0); } }
  .nbd-mode-vista .fadeOutRightBig {
    animation-name: fadeOutRightBig; }

@keyframes fadeOutUpBig {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(0, -2000px, 0); } }
  .nbd-mode-vista .fadeOutUpBig {
    animation-name: fadeOutUpBig; }

@keyframes flip {
  0% {
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    animation-timing-function: ease-out; }
  40% {
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    animation-timing-function: ease-out; }
  50% {
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    animation-timing-function: ease-in; }
  80% {
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    animation-timing-function: ease-in; }
  100% {
    transform: perspective(400px);
    animation-timing-function: ease-in; } }
  .nbd-mode-vista .animated.flip {
    backface-visibility: visible;
    animation-name: flip; }

@keyframes flipInX {
  0% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0; }
  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transition-timing-function: ease-in; }
  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1; }
  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg); }
  100% {
    transform: perspective(400px); } }
  .nbd-mode-vista .flipInX {
    backface-visibility: visible !important;
    animation-name: flipInX; }

@keyframes flipInY {
  0% {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0; }
  40% {
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transition-timing-function: ease-in; }
  60% {
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1; }
  80% {
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg); }
  100% {
    transform: perspective(400px); } }
  .nbd-mode-vista .flipInY {
    backface-visibility: visible !important;
    animation-name: flipInY; }

@keyframes flipOutX {
  0% {
    transform: perspective(400px); }
  30% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1; }
  100% {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0; } }
  .nbd-mode-vista .flipOutX {
    animation-name: flipOutX;
    backface-visibility: visible !important; }

@keyframes flipOutY {
  0% {
    transform: perspective(400px); }
  30% {
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1; }
  100% {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0; } }
  .nbd-mode-vista .flipOutY {
    backface-visibility: visible !important;
    animation-name: flipOutY; }

@keyframes lightSpeedIn {
  0% {
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0; }
  60% {
    transform: skewX(20deg);
    opacity: 1; }
  80% {
    transform: skewX(-5deg);
    opacity: 1; }
  100% {
    transform: none;
    opacity: 1; } }
  .nbd-mode-vista .lightSpeedIn {
    animation-name: lightSpeedIn;
    animation-timing-function: ease-out; }

@keyframes lightSpeedOut {
  0% {
    opacity: 1; }
  100% {
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0; } }
  .nbd-mode-vista .lightSpeedOut {
    animation-name: lightSpeedOut;
    animation-timing-function: ease-in; }

@keyframes rotateIn {
  0% {
    transform-origin: center;
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0; }
  100% {
    transform-origin: center;
    transform: none;
    opacity: 1; } }
  .nbd-mode-vista .rotateIn {
    animation-name: rotateIn; }

@keyframes rotateInDownLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; }
  100% {
    transform-origin: left bottom;
    transform: none;
    opacity: 1; } }
  .nbd-mode-vista .rotateInDownLeft {
    animation-name: rotateInDownLeft; }

@keyframes rotateInDownRight {
  0% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; }
  100% {
    transform-origin: right bottom;
    transform: none;
    opacity: 1; } }
  .nbd-mode-vista .rotateInDownRight {
    animation-name: rotateInDownRight; }

@keyframes rotateInUpLeft {
  0% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; }
  100% {
    transform-origin: left bottom;
    transform: none;
    opacity: 1; } }
  .nbd-mode-vista .rotateInUpLeft {
    animation-name: rotateInUpLeft; }

@keyframes rotateInUpRight {
  0% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0; }
  100% {
    transform-origin: right bottom;
    transform: none;
    opacity: 1; } }
  .nbd-mode-vista .rotateInUpRight {
    animation-name: rotateInUpRight; }

@keyframes rotateOut {
  0% {
    transform-origin: center;
    opacity: 1; }
  100% {
    transform-origin: center;
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0; } }
  .nbd-mode-vista .rotateOut {
    animation-name: rotateOut; }

@keyframes rotateOutDownLeft {
  0% {
    transform-origin: left bottom;
    opacity: 1; }
  100% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0; } }
  .nbd-mode-vista .rotateOutDownLeft {
    animation-name: rotateOutDownLeft; }

@keyframes rotateOutDownRight {
  0% {
    transform-origin: right bottom;
    opacity: 1; }
  100% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; } }
  .nbd-mode-vista .rotateOutDownRight {
    animation-name: rotateOutDownRight; }

@keyframes rotateOutUpLeft {
  0% {
    transform-origin: left bottom;
    opacity: 1; }
  100% {
    transform-origin: left bottom;
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0; } }
  .nbd-mode-vista .rotateOutUpLeft {
    animation-name: rotateOutUpLeft; }

@keyframes rotateOutUpRight {
  0% {
    transform-origin: right bottom;
    opacity: 1; }
  100% {
    transform-origin: right bottom;
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0; } }
  .nbd-mode-vista .rotateOutUpRight {
    animation-name: rotateOutUpRight; }

@keyframes hinge {
  0% {
    transform-origin: top left;
    animation-timing-function: ease-in-out; }
  20%, 60% {
    transform: rotate3d(0, 0, 1, 80deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out; }
  40%, 80% {
    transform: rotate3d(0, 0, 1, 60deg);
    transform-origin: top left;
    animation-timing-function: ease-in-out;
    opacity: 1; }
  100% {
    transform: translate3d(0, 700px, 0);
    opacity: 0; } }
  .nbd-mode-vista .hinge {
    animation-name: hinge; }

@keyframes rollIn {
  0% {
    opacity: 0;
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg); }
  100% {
    opacity: 1;
    transform: none; } }
  .nbd-mode-vista .rollIn {
    animation-name: rollIn; }

@keyframes rollOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0;
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg); } }
  .nbd-mode-vista .rollOut {
    animation-name: rollOut; }

@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  50% {
    opacity: 1; } }
  .nbd-mode-vista .zoomIn {
    animation-name: zoomIn; }

@keyframes zoomInDown {
  0% {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
  .nbd-mode-vista .zoomInDown {
    animation-name: zoomInDown; }

@keyframes zoomInLeft {
  0% {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
  .nbd-mode-vista .zoomInLeft {
    animation-name: zoomInLeft; }

@keyframes zoomInRight {
  0% {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
  .nbd-mode-vista .zoomInRight {
    animation-name: zoomInRight; }

@keyframes zoomInUp {
  0% {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
  .nbd-mode-vista .zoomInUp {
    animation-name: zoomInUp; }

@keyframes zoomOut {
  0% {
    opacity: 1; }
  50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3); }
  100% {
    opacity: 0; } }
  .nbd-mode-vista .zoomOut {
    animation-name: zoomOut; }

@keyframes zoomOutDown {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  100% {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
  .nbd-mode-vista .zoomOutDown {
    animation-name: zoomOutDown; }

@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0); }
  100% {
    opacity: 0;
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform-origin: left center; } }
  .nbd-mode-vista .zoomOutLeft {
    animation-name: zoomOutLeft; }

@keyframes zoomOutRight {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0); }
  100% {
    opacity: 0;
    transform: scale(0.1) translate3d(2000px, 0, 0);
    transform-origin: right center; } }
  .nbd-mode-vista .zoomOutRight {
    animation-name: zoomOutRight; }

@keyframes zoomOutUp {
  40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19); }
  100% {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform-origin: center bottom;
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1); } }
  .nbd-mode-vista .zoomOutUp {
    animation-name: zoomOutUp; }
  .nbd-mode-vista .sp-container {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    /* https://github.com/bgrins/spectrum/issues/40 */
    z-index: 9999994;
    overflow: hidden; }
  .nbd-mode-vista .sp-container.sp-flat {
    position: relative; }
  .nbd-mode-vista .sp-container,
  .nbd-mode-vista .sp-container * {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box; }
  .nbd-mode-vista .sp-top {
    position: relative;
    width: 100%;
    display: inline-block; }
  .nbd-mode-vista .sp-top-inner {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0; }
  .nbd-mode-vista .sp-color {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 20%; }
  .nbd-mode-vista .sp-hue {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 84%;
    height: 100%; }
  .nbd-mode-vista .sp-clear-enabled .sp-hue {
    top: 33px;
    height: 77.5%; }
  .nbd-mode-vista .sp-fill {
    padding-top: 80%; }
  .nbd-mode-vista .sp-sat, .nbd-mode-vista .sp-val {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0; }
  .nbd-mode-vista .sp-alpha-enabled .sp-top {
    margin-bottom: 18px; }
  .nbd-mode-vista .sp-alpha-enabled .sp-alpha {
    display: block; }
  .nbd-mode-vista .sp-alpha-handle {
    position: absolute;
    top: -4px;
    bottom: -4px;
    width: 6px;
    left: 50%;
    cursor: pointer;
    border: 1px solid black;
    background: white;
    opacity: .8; }
  .nbd-mode-vista .sp-alpha {
    display: none;
    position: absolute;
    bottom: -14px;
    right: 0;
    left: 0;
    height: 8px; }
  .nbd-mode-vista .sp-alpha-inner {
    border: solid 1px #333; }
  .nbd-mode-vista .sp-clear {
    display: none; }
  .nbd-mode-vista .sp-clear.sp-clear-display {
    background-position: center; }
  .nbd-mode-vista .sp-clear-enabled .sp-clear {
    display: block;
    position: absolute;
    top: 0px;
    right: 0;
    bottom: 0;
    left: 84%;
    height: 28px; }
  .nbd-mode-vista .sp-container, .nbd-mode-vista .sp-replacer, .nbd-mode-vista .sp-preview, .nbd-mode-vista .sp-dragger, .nbd-mode-vista .sp-slider, .nbd-mode-vista .sp-alpha, .nbd-mode-vista .sp-clear, .nbd-mode-vista .sp-alpha-handle, .nbd-mode-vista .sp-container.sp-dragging .sp-input, .nbd-mode-vista .sp-container button {
    -webkit-user-select: none;
    -moz-user-select: -moz-none;
    -o-user-select: none;
    user-select: none; }
  .nbd-mode-vista .sp-container.sp-input-disabled .sp-input-container {
    display: none; }
  .nbd-mode-vista .sp-container.sp-buttons-disabled .sp-button-container {
    display: none; }
  .nbd-mode-vista .sp-container.sp-palette-buttons-disabled .sp-palette-button-container {
    display: none; }
  .nbd-mode-vista .sp-palette-only .sp-picker-container {
    display: none; }
  .nbd-mode-vista .sp-palette-disabled .sp-palette-container {
    display: none; }
  .nbd-mode-vista .sp-initial-disabled .sp-initial {
    display: none; }
  .nbd-mode-vista .sp-sat {
    background-image: -webkit-gradient(linear, 0 0, 100% 0, from(#FFF), to(rgba(204, 154, 129, 0)));
    background-image: -webkit-linear-gradient(left, #FFF, rgba(204, 154, 129, 0));
    background-image: -moz-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
    background-image: -o-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
    background-image: -ms-linear-gradient(left, #fff, rgba(204, 154, 129, 0));
    background-image: linear-gradient(to right, #fff, rgba(204, 154, 129, 0));
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType = 1, startColorstr=#FFFFFFFF, endColorstr=#00CC9A81)";
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType = 1, startColorstr='#FFFFFFFF', endColorstr='#00CC9A81'); }
  .nbd-mode-vista .sp-val {
    background-image: -webkit-gradient(linear, 0 100%, 0 0, from(#000000), to(rgba(204, 154, 129, 0)));
    background-image: -webkit-linear-gradient(bottom, #000000, rgba(204, 154, 129, 0));
    background-image: -moz-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
    background-image: -o-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
    background-image: -ms-linear-gradient(bottom, #000, rgba(204, 154, 129, 0));
    background-image: linear-gradient(to top, #000, rgba(204, 154, 129, 0));
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#00CC9A81, endColorstr=#FF000000)";
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00CC9A81', endColorstr='#FF000000'); }
  .nbd-mode-vista .sp-hue {
    background: -moz-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
    background: -ms-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
    background: -o-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
    background: -webkit-gradient(linear, left top, left bottom, from(#ff0000), color-stop(0.17, #ffff00), color-stop(0.33, #00ff00), color-stop(0.5, #00ffff), color-stop(0.67, #0000ff), color-stop(0.83, #ff00ff), to(#ff0000));
    background: -webkit-linear-gradient(top, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%);
    background: linear-gradient(to bottom, #ff0000 0%, #ffff00 17%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 83%, #ff0000 100%); }
  .nbd-mode-vista .sp-1 {
    height: 17%;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0000', endColorstr='#ffff00'); }
  .nbd-mode-vista .sp-2 {
    height: 16%;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffff00', endColorstr='#00ff00'); }
  .nbd-mode-vista .sp-3 {
    height: 17%;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ff00', endColorstr='#00ffff'); }
  .nbd-mode-vista .sp-4 {
    height: 17%;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ffff', endColorstr='#0000ff'); }
  .nbd-mode-vista .sp-5 {
    height: 16%;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0000ff', endColorstr='#ff00ff'); }
  .nbd-mode-vista .sp-6 {
    height: 17%;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff00ff', endColorstr='#ff0000'); }
  .nbd-mode-vista .sp-hidden {
    display: none !important; }
  .nbd-mode-vista .sp-cf:before, .nbd-mode-vista .sp-cf:after {
    content: "";
    display: table; }
  .nbd-mode-vista .sp-cf:after {
    clear: both; }
  .nbd-mode-vista .sp-cf {
    *zoom: 1; }
  @media (max-device-width: 480px) {
    .nbd-mode-vista .sp-color {
      right: 40%; }
    .nbd-mode-vista .sp-hue {
      left: 63%; }
    .nbd-mode-vista .sp-fill {
      padding-top: 60%; } }
  .nbd-mode-vista .sp-dragger {
    border-radius: 5px;
    height: 5px;
    width: 5px;
    border: 1px solid #fff;
    background: #000;
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0; }
  .nbd-mode-vista .sp-slider {
    position: absolute;
    top: 0;
    cursor: pointer;
    height: 3px;
    left: -1px;
    right: -1px;
    border: 1px solid #000;
    background: white;
    opacity: .8; }
  .nbd-mode-vista .sp-container {
    border-radius: 0;
    background-color: #ECECEC;
    border: solid 1px #f0c49B;
    padding: 0; }
  .nbd-mode-vista .sp-container, .nbd-mode-vista .sp-container button, .nbd-mode-vista .sp-container input, .nbd-mode-vista .sp-color, .nbd-mode-vista .sp-hue, .nbd-mode-vista .sp-clear {
    font: normal 12px "Lucida Grande", "Lucida Sans Unicode", "Lucida Sans", Geneva, Verdana, sans-serif;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box; }
  .nbd-mode-vista .sp-top {
    margin-bottom: 3px; }
  .nbd-mode-vista .sp-color, .nbd-mode-vista .sp-hue, .nbd-mode-vista .sp-clear {
    border: solid 1px #666; }
  .nbd-mode-vista .sp-input-container {
    float: right;
    width: 100px;
    margin-bottom: 4px; }
  .nbd-mode-vista .sp-initial-disabled .sp-input-container {
    width: 100%; }
  .nbd-mode-vista .sp-input {
    font-size: 12px !important;
    border: 1px inset;
    padding: 4px 5px;
    margin: 0;
    width: 100%;
    background: transparent;
    border-radius: 3px;
    color: #222; }
  .nbd-mode-vista .sp-input:focus {
    border: 1px solid orange; }
  .nbd-mode-vista .sp-input.sp-validation-error {
    border: 1px solid red;
    background: #fdd; }
  .nbd-mode-vista .sp-picker-container, .nbd-mode-vista .sp-palette-container {
    float: left;
    position: relative;
    padding: 10px;
    padding-bottom: 300px;
    margin-bottom: -290px; }
  .nbd-mode-vista .sp-picker-container {
    width: 172px;
    border-left: solid 1px #fff; }
  .nbd-mode-vista .sp-palette-container {
    border-right: solid 1px #ccc; }
  .nbd-mode-vista .sp-palette-only .sp-palette-container {
    border: 0; }
  .nbd-mode-vista .sp-palette .sp-thumb-el {
    display: block;
    position: relative;
    float: left;
    width: 24px;
    height: 15px;
    margin: 3px;
    cursor: pointer;
    border: solid 2px transparent; }
  .nbd-mode-vista .sp-palette .sp-thumb-el:hover, .nbd-mode-vista .sp-palette .sp-thumb-el.sp-thumb-active {
    border-color: orange; }
  .nbd-mode-vista .sp-thumb-el {
    position: relative; }
  .nbd-mode-vista .sp-initial {
    float: left;
    border: solid 1px #333; }
  .nbd-mode-vista .sp-initial span {
    width: 30px;
    height: 25px;
    border: none;
    display: block;
    float: left;
    margin: 0; }
  .nbd-mode-vista .sp-initial .sp-clear-display {
    background-position: center; }
  .nbd-mode-vista .sp-palette-button-container,
  .nbd-mode-vista .sp-button-container {
    float: right; }
  .nbd-mode-vista .sp-replacer {
    margin: 0;
    overflow: hidden;
    cursor: pointer;
    padding: 4px;
    display: inline-block;
    *zoom: 1;
    *display: inline;
    border: solid 1px #91765d;
    background: #eee;
    color: #333;
    vertical-align: middle; }
  .nbd-mode-vista .sp-replacer:hover, .nbd-mode-vista .sp-replacer.sp-active {
    border-color: #F0C49B;
    color: #111; }
  .nbd-mode-vista .sp-replacer.sp-disabled {
    cursor: default;
    border-color: silver;
    color: silver; }
  .nbd-mode-vista .sp-dd {
    padding: 2px 0;
    height: 16px;
    line-height: 16px;
    float: left;
    font-size: 10px; }
  .nbd-mode-vista .sp-preview {
    position: relative;
    width: 25px;
    height: 20px;
    border: solid 1px #222;
    margin-right: 5px;
    float: left;
    z-index: 0; }
  .nbd-mode-vista .sp-palette {
    *width: 220px;
    max-width: 220px; }
  .nbd-mode-vista .sp-palette .sp-thumb-el {
    width: 16px;
    height: 16px;
    margin: 2px 1px;
    border: solid 1px #d0d0d0; }
  .nbd-mode-vista .sp-container {
    padding-bottom: 0; }
  .nbd-mode-vista .sp-container button {
    background-color: #eeeeee;
    background-image: -webkit-linear-gradient(top, #eeeeee, #cccccc);
    background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
    background-image: -ms-linear-gradient(top, #eeeeee, #cccccc);
    background-image: -o-linear-gradient(top, #eeeeee, #cccccc);
    background-image: linear-gradient(to bottom, #eeeeee, #cccccc);
    border: 1px solid #ccc;
    border-bottom: 1px solid #bbb;
    border-radius: 3px;
    color: #333;
    font-size: 14px;
    line-height: 1;
    padding: 5px 4px;
    text-align: center;
    text-shadow: 0 1px 0 #eee;
    vertical-align: middle; }
  .nbd-mode-vista .sp-container button:hover {
    background-color: #dddddd;
    background-image: -webkit-linear-gradient(top, #dddddd, #bbbbbb);
    background-image: -moz-linear-gradient(top, #dddddd, #bbbbbb);
    background-image: -ms-linear-gradient(top, #dddddd, #bbbbbb);
    background-image: -o-linear-gradient(top, #dddddd, #bbbbbb);
    background-image: linear-gradient(to bottom, #dddddd, #bbbbbb);
    border: 1px solid #bbb;
    border-bottom: 1px solid #999;
    cursor: pointer;
    text-shadow: 0 1px 0 #ddd; }
  .nbd-mode-vista .sp-container button:active {
    border: 1px solid #aaa;
    border-bottom: 1px solid #888;
    -webkit-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
    -moz-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
    -ms-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
    -o-box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee;
    box-shadow: inset 0 0 5px 2px #aaaaaa, 0 1px 0 0 #eeeeee; }
  .nbd-mode-vista .sp-cancel {
    font-size: 11px;
    color: #d93f3f !important;
    margin: 0;
    padding: 2px;
    margin-right: 5px;
    vertical-align: middle;
    text-decoration: none; }
  .nbd-mode-vista .sp-cancel:hover {
    color: #d93f3f !important;
    text-decoration: underline; }
  .nbd-mode-vista .sp-palette span:hover, .nbd-mode-vista .sp-palette span.sp-thumb-active {
    border-color: #000; }
  .nbd-mode-vista .sp-preview, .nbd-mode-vista .sp-alpha, .nbd-mode-vista .sp-thumb-el {
    position: relative;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==); }
  .nbd-mode-vista .sp-preview-inner, .nbd-mode-vista .sp-alpha-inner, .nbd-mode-vista .sp-thumb-inner {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0; }
  .nbd-mode-vista .sp-palette .sp-thumb-inner {
    background-position: 50% 50%;
    background-repeat: no-repeat; }
  .nbd-mode-vista .sp-palette .sp-thumb-light.sp-thumb-active .sp-thumb-inner {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAIVJREFUeNpiYBhsgJFMffxAXABlN5JruT4Q3wfi/0DsT64h8UD8HmpIPCWG/KemIfOJCUB+Aoacx6EGBZyHBqI+WsDCwuQ9mhxeg2A210Ntfo8klk9sOMijaURm7yc1UP2RNCMbKE9ODK1HM6iegYLkfx8pligC9lCD7KmRof0ZhjQACDAAceovrtpVBRkAAAAASUVORK5CYII=); }
  .nbd-mode-vista .sp-palette .sp-thumb-dark.sp-thumb-active .sp-thumb-inner {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAadEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjEwMPRyoQAAAMdJREFUOE+tkgsNwzAMRMugEAahEAahEAZhEAqlEAZhEAohEAYh81X2dIm8fKpEspLGvudPOsUYpxE2BIJCroJmEW9qJ+MKaBFhEMNabSy9oIcIPwrB+afvAUFoK4H0tMaQ3XtlrggDhOVVMuT4E5MMG0FBbCEYzjYT7OxLEvIHQLY2zWwQ3D+9luyOQTfKDiFD3iUIfPk8VqrKjgAiSfGFPecrg6HN6m/iBcwiDAo7WiBeawa+Kwh7tZoSCGLMqwlSAzVDhoK+6vH4G0P5wdkAAAAASUVORK5CYII=); }
  .nbd-mode-vista .sp-clear-display {
    background-repeat: no-repeat;
    background-position: center;
    background-image: url(data:image/gif;base64,R0lGODlhFAAUAPcAAAAAAJmZmZ2dnZ6enqKioqOjo6SkpKWlpaampqenp6ioqKmpqaqqqqurq/Hx8fLy8vT09PX19ff39/j4+Pn5+fr6+vv7+wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACH5BAEAAP8ALAAAAAAUABQAAAihAP9FoPCvoMGDBy08+EdhQAIJCCMybCDAAYUEARBAlFiQQoMABQhKUJBxY0SPICEYHBnggEmDKAuoPMjS5cGYMxHW3IiT478JJA8M/CjTZ0GgLRekNGpwAsYABHIypcAgQMsITDtWJYBR6NSqMico9cqR6tKfY7GeBCuVwlipDNmefAtTrkSzB1RaIAoXodsABiZAEFB06gIBWC1mLVgBa0AAOw==); }
  .nbd-mode-vista .ui-helper-hidden {
    display: none; }
  .nbd-mode-vista .ui-helper-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px; }
  .nbd-mode-vista .ui-helper-reset {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    line-height: 1.3;
    text-decoration: none;
    font-size: 100%;
    list-style: none; }
  .nbd-mode-vista .ui-helper-clearfix:before, .nbd-mode-vista .ui-helper-clearfix:after {
    content: "";
    display: table;
    border-collapse: collapse; }
  .nbd-mode-vista .ui-helper-clearfix:after {
    clear: both; }
  .nbd-mode-vista .ui-helper-clearfix {
    min-height: 0; }
  .nbd-mode-vista .ui-helper-zfix {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0;
    filter: Alpha(Opacity=0); }
  .nbd-mode-vista .ui-front {
    z-index: 100; }
  .nbd-mode-vista .ui-state-disabled {
    cursor: default !important; }
  .nbd-mode-vista .ui-icon {
    display: block;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat; }
  .nbd-mode-vista .ui-widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%; }
  .nbd-mode-vista .ui-accordion .ui-accordion-header {
    display: block;
    cursor: pointer;
    position: relative;
    margin-top: 2px;
    padding: .5em .5em .5em .7em;
    min-height: 0; }
  .nbd-mode-vista .ui-accordion .ui-accordion-icons {
    padding-left: 2.2em; }
  .nbd-mode-vista .ui-accordion .ui-accordion-noicons {
    padding-left: .7em; }
  .nbd-mode-vista .ui-accordion .ui-accordion-icons .ui-accordion-icons {
    padding-left: 2.2em; }
  .nbd-mode-vista .ui-accordion .ui-accordion-header .ui-accordion-header-icon {
    position: absolute;
    left: .5em;
    top: 50%;
    margin-top: -8px; }
  .nbd-mode-vista .ui-accordion .ui-accordion-content {
    padding: 1em 2.2em;
    border-top: 0;
    overflow: auto; }
  .nbd-mode-vista .ui-autocomplete {
    position: absolute;
    top: 0;
    left: 0;
    cursor: default; }
  .nbd-mode-vista .ui-button {
    display: inline-block;
    position: relative;
    padding: 0;
    line-height: normal;
    margin-right: .1em;
    cursor: pointer;
    vertical-align: middle;
    text-align: center;
    overflow: visible; }
  .nbd-mode-vista .ui-button, .nbd-mode-vista .ui-button:link, .nbd-mode-vista .ui-button:visited, .nbd-mode-vista .ui-button:hover, .nbd-mode-vista .ui-button:active {
    text-decoration: none; }
  .nbd-mode-vista .ui-button-icon-only {
    width: 2.2em; }
  .nbd-mode-vista button.ui-button-icon-only {
    width: 2.4em; }
  .nbd-mode-vista .ui-button-icons-only {
    width: 3.4em; }
  .nbd-mode-vista button.ui-button-icons-only {
    width: 3.7em; }
  .nbd-mode-vista .ui-button .ui-button-text {
    display: block;
    line-height: normal; }
  .nbd-mode-vista .ui-button-text-only .ui-button-text {
    padding: .4em 1em; }
  .nbd-mode-vista .ui-button-icon-only .ui-button-text, .nbd-mode-vista .ui-button-icons-only .ui-button-text {
    padding: .4em;
    text-indent: -9999999px; }
  .nbd-mode-vista .ui-button-text-icon-primary .ui-button-text, .nbd-mode-vista .ui-button-text-icons .ui-button-text {
    padding: .4em 1em .4em 2.1em; }
  .nbd-mode-vista .ui-button-text-icon-secondary .ui-button-text, .nbd-mode-vista .ui-button-text-icons .ui-button-text {
    padding: .4em 2.1em .4em 1em; }
  .nbd-mode-vista .ui-button-text-icons .ui-button-text {
    padding-left: 2.1em;
    padding-right: 2.1em; }
  .nbd-mode-vista input.ui-button {
    padding: .4em 1em; }
  .nbd-mode-vista .ui-button-icon-only .ui-icon, .nbd-mode-vista .ui-button-text-icon-primary .ui-icon, .nbd-mode-vista .ui-button-text-icon-secondary .ui-icon, .nbd-mode-vista .ui-button-text-icons .ui-icon, .nbd-mode-vista .ui-button-icons-only .ui-icon {
    position: absolute;
    top: 50%;
    margin-top: -8px; }
  .nbd-mode-vista .ui-button-icon-only .ui-icon {
    left: 50%;
    margin-left: -8px; }
  .nbd-mode-vista .ui-button-text-icon-primary .ui-button-icon-primary, .nbd-mode-vista .ui-button-text-icons .ui-button-icon-primary, .nbd-mode-vista .ui-button-icons-only .ui-button-icon-primary {
    left: .5em; }
  .nbd-mode-vista .ui-button-text-icon-secondary .ui-button-icon-secondary, .nbd-mode-vista .ui-button-text-icons .ui-button-icon-secondary, .nbd-mode-vista .ui-button-icons-only .ui-button-icon-secondary {
    right: .5em; }
  .nbd-mode-vista .ui-buttonset {
    margin-right: 7px; }
  .nbd-mode-vista .ui-buttonset .ui-button {
    margin-left: 0;
    margin-right: -.3em; }
  .nbd-mode-vista input.ui-button::-moz-focus-inner, .nbd-mode-vista button.ui-button::-moz-focus-inner {
    border: 0;
    padding: 0; }
  .nbd-mode-vista .ui-datepicker {
    width: 17em;
    padding: .2em .2em 0;
    display: none; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: .2em 0; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-prev, .nbd-mode-vista .ui-datepicker .ui-datepicker-next {
    position: absolute;
    top: 2px;
    width: 1.8em;
    height: 1.8em; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-prev-hover, .nbd-mode-vista .ui-datepicker .ui-datepicker-next-hover {
    top: 1px; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-prev {
    left: 2px; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-next {
    right: 2px; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-prev-hover {
    left: 1px; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-next-hover {
    right: 1px; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-prev span, .nbd-mode-vista .ui-datepicker .ui-datepicker-next span {
    display: block;
    position: absolute;
    left: 50%;
    margin-left: -8px;
    top: 50%;
    margin-top: -8px; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-title {
    margin: 0 2.3em;
    line-height: 1.8em;
    text-align: center; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-title select {
    font-size: 1em;
    margin: 1px 0; }
  .nbd-mode-vista .ui-datepicker select.ui-datepicker-month, .nbd-mode-vista .ui-datepicker select.ui-datepicker-year {
    width: 49%; }
  .nbd-mode-vista .ui-datepicker table {
    width: 100%;
    font-size: .9em;
    border-collapse: collapse;
    margin: 0 0 .4em; }
  .nbd-mode-vista .ui-datepicker th {
    padding: .7em .3em;
    text-align: center;
    font-weight: bold;
    border: 0; }
  .nbd-mode-vista .ui-datepicker td {
    border: 0;
    padding: 1px; }
  .nbd-mode-vista .ui-datepicker td span, .nbd-mode-vista .ui-datepicker td a {
    display: block;
    padding: .2em;
    text-align: right;
    text-decoration: none; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-buttonpane {
    background-image: none;
    margin: .7em 0 0 0;
    padding: 0 .2em;
    border-left: 0;
    border-right: 0;
    border-bottom: 0; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-buttonpane button {
    float: right;
    margin: .5em .2em .4em;
    cursor: pointer;
    padding: .2em .6em .3em .6em;
    width: auto;
    overflow: visible; }
  .nbd-mode-vista .ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
    float: left; }
  .nbd-mode-vista .ui-datepicker.ui-datepicker-multi {
    width: auto; }
  .nbd-mode-vista .ui-datepicker-multi .ui-datepicker-group {
    float: left; }
  .nbd-mode-vista .ui-datepicker-multi .ui-datepicker-group table {
    width: 95%;
    margin: 0 auto .4em; }
  .nbd-mode-vista .ui-datepicker-multi-2 .ui-datepicker-group {
    width: 50%; }
  .nbd-mode-vista .ui-datepicker-multi-3 .ui-datepicker-group {
    width: 33.3%; }
  .nbd-mode-vista .ui-datepicker-multi-4 .ui-datepicker-group {
    width: 25%; }
  .nbd-mode-vista .ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header, .nbd-mode-vista .ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
    border-left-width: 0; }
  .nbd-mode-vista .ui-datepicker-multi .ui-datepicker-buttonpane {
    clear: left; }
  .nbd-mode-vista .ui-datepicker-row-break {
    clear: both;
    width: 100%;
    font-size: 0; }
  .nbd-mode-vista .ui-datepicker-rtl {
    direction: rtl; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-prev {
    right: 2px;
    left: auto; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-next {
    left: 2px;
    right: auto; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-prev:hover {
    right: 1px;
    left: auto; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-next:hover {
    left: 1px;
    right: auto; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-buttonpane {
    clear: right; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-buttonpane button {
    float: left; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current, .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-group {
    float: right; }
  .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header, .nbd-mode-vista .ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
    border-right-width: 0;
    border-left-width: 1px; }
  .nbd-mode-vista .ui-dialog {
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    padding: .2em;
    outline: 0; }
  .nbd-mode-vista .ui-dialog .ui-dialog-titlebar {
    padding: .4em 1em;
    position: relative; }
  .nbd-mode-vista .ui-dialog .ui-dialog-title {
    float: left;
    margin: .1em 0;
    white-space: nowrap;
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis; }
  .nbd-mode-vista .ui-dialog .ui-dialog-titlebar-close {
    position: absolute;
    right: .3em;
    top: 50%;
    width: 20px;
    margin: -10px 0 0 0;
    padding: 1px;
    height: 20px; }
  .nbd-mode-vista .ui-dialog .ui-dialog-content {
    position: relative;
    border: 0;
    padding: .5em 1em;
    background: none;
    overflow: auto; }
  .nbd-mode-vista .ui-dialog .ui-dialog-buttonpane {
    text-align: left;
    border-width: 1px 0 0 0;
    background-image: none;
    margin-top: .5em;
    padding: .3em 1em .5em .4em; }
  .nbd-mode-vista .ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
    float: right; }
  .nbd-mode-vista .ui-dialog .ui-dialog-buttonpane button {
    margin: .5em .4em .5em 0;
    cursor: pointer; }
  .nbd-mode-vista .ui-dialog .ui-resizable-se {
    width: 12px;
    height: 12px;
    right: -5px;
    bottom: -5px;
    background-position: 16px 16px; }
  .nbd-mode-vista .ui-draggable .ui-dialog-titlebar {
    cursor: move; }
  .nbd-mode-vista .ui-menu {
    list-style: none;
    padding: 2px;
    margin: 0;
    display: block;
    outline: none; }
  .nbd-mode-vista .ui-menu .ui-menu {
    margin-top: -3px;
    position: absolute; }
  .nbd-mode-vista .ui-menu .ui-menu-item {
    margin: 0;
    padding: 0;
    width: 100%;
    list-style-image: url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7); }
  .nbd-mode-vista .ui-menu .ui-menu-divider {
    margin: 5px -2px 5px -2px;
    height: 0;
    font-size: 0;
    line-height: 0;
    border-width: 1px 0 0 0; }
  .nbd-mode-vista .ui-menu .ui-menu-item a {
    text-decoration: none;
    display: block;
    padding: 2px .4em;
    line-height: 1.5;
    min-height: 0;
    font-weight: normal; }
  .nbd-mode-vista .ui-menu .ui-menu-item a.ui-state-focus, .nbd-mode-vista .ui-menu .ui-menu-item a.ui-state-active {
    font-weight: normal;
    margin: -1px; }
  .nbd-mode-vista .ui-menu .ui-state-disabled {
    font-weight: normal;
    margin: .4em 0 .2em;
    line-height: 1.5; }
  .nbd-mode-vista .ui-menu .ui-state-disabled a {
    cursor: default; }
  .nbd-mode-vista .ui-menu-icons {
    position: relative; }
  .nbd-mode-vista .ui-menu-icons .ui-menu-item a {
    position: relative;
    padding-left: 2em; }
  .nbd-mode-vista .ui-menu .ui-icon {
    position: absolute;
    top: .2em;
    left: .2em; }
  .nbd-mode-vista .ui-menu .ui-menu-icon {
    position: static;
    float: right; }
  .nbd-mode-vista .ui-progressbar {
    height: 2em;
    text-align: left;
    overflow: hidden; }
  .nbd-mode-vista .ui-progressbar .ui-progressbar-value {
    margin: -1px;
    height: 100%; }
  .nbd-mode-vista .ui-progressbar .ui-progressbar-overlay {
    background: url("images/animated-overlay.gif");
    height: 100%;
    filter: alpha(opacity=25);
    opacity: 0.25; }
  .nbd-mode-vista .ui-progressbar-indeterminate .ui-progressbar-value {
    background-image: none; }
  .nbd-mode-vista .ui-resizable {
    position: relative; }
  .nbd-mode-vista .ui-resizable-handle {
    position: absolute;
    font-size: 0.1px;
    display: block; }
  .nbd-mode-vista .ui-resizable-disabled .ui-resizable-handle, .nbd-mode-vista .ui-resizable-autohide .ui-resizable-handle {
    display: none; }
  .nbd-mode-vista .ui-resizable-n {
    cursor: n-resize;
    height: 7px;
    width: 100%;
    top: -5px;
    left: 0; }
  .nbd-mode-vista .ui-resizable-s {
    cursor: s-resize;
    height: 7px;
    width: 100%;
    bottom: -5px;
    left: 0; }
  .nbd-mode-vista .ui-resizable-e {
    cursor: e-resize;
    width: 7px;
    right: -5px;
    top: 0;
    height: 100%; }
  .nbd-mode-vista .ui-resizable-w {
    cursor: w-resize;
    width: 7px;
    left: -5px;
    top: 0;
    height: 100%; }
  .nbd-mode-vista .ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px; }
  .nbd-mode-vista .ui-resizable-sw {
    cursor: sw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    bottom: -5px; }
  .nbd-mode-vista .ui-resizable-nw {
    cursor: nw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    top: -5px; }
  .nbd-mode-vista .ui-resizable-ne {
    cursor: ne-resize;
    width: 9px;
    height: 9px;
    right: -5px;
    top: -5px; }
  .nbd-mode-vista .ui-selectable-helper {
    position: absolute;
    z-index: 100;
    border: 1px dotted black; }
  .nbd-mode-vista .ui-slider {
    position: relative;
    text-align: left; }
  .nbd-mode-vista .ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 1.2em;
    height: 1.2em;
    cursor: default; }
  .nbd-mode-vista .ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0; }
  .nbd-mode-vista .ui-slider.ui-state-disabled .ui-slider-handle, .nbd-mode-vista .ui-slider.ui-state-disabled .ui-slider-range {
    filter: inherit; }
  .nbd-mode-vista .ui-slider-horizontal {
    height: .8em; }
  .nbd-mode-vista .ui-slider-horizontal .ui-slider-handle {
    top: -.3em;
    margin-left: -.6em; }
  .nbd-mode-vista .ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%; }
  .nbd-mode-vista .ui-slider-horizontal .ui-slider-range-min {
    left: 0; }
  .nbd-mode-vista .ui-slider-horizontal .ui-slider-range-max {
    right: 0; }
  .nbd-mode-vista .ui-slider-vertical {
    width: .8em;
    height: 100px; }
  .nbd-mode-vista .ui-slider-vertical .ui-slider-handle {
    left: -.3em;
    margin-left: 0;
    margin-bottom: -.6em; }
  .nbd-mode-vista .ui-slider-vertical .ui-slider-range {
    left: 0;
    width: 100%; }
  .nbd-mode-vista .ui-slider-vertical .ui-slider-range-min {
    bottom: 0; }
  .nbd-mode-vista .ui-slider-vertical .ui-slider-range-max {
    top: 0; }
  .nbd-mode-vista .ui-spinner {
    position: relative;
    display: inline-block;
    overflow: hidden;
    padding: 0;
    vertical-align: middle; }
  .nbd-mode-vista .ui-spinner-input {
    border: none;
    background: none;
    color: inherit;
    padding: 0;
    margin: .2em 0;
    vertical-align: middle;
    margin-left: .4em;
    margin-right: 22px; }
  .nbd-mode-vista .ui-spinner-button {
    width: 16px;
    height: 50%;
    font-size: .5em;
    padding: 0;
    margin: 0;
    text-align: center;
    position: absolute;
    cursor: default;
    display: block;
    overflow: hidden;
    right: 0; }
  .nbd-mode-vista .ui-spinner a.ui-spinner-button {
    border-top: none;
    border-bottom: none;
    border-right: none; }
  .nbd-mode-vista .ui-spinner .ui-icon {
    position: absolute;
    margin-top: -8px;
    top: 50%;
    left: 0; }
  .nbd-mode-vista .ui-spinner-up {
    top: 0; }
  .nbd-mode-vista .ui-spinner-down {
    bottom: 0; }
  .nbd-mode-vista .ui-spinner .ui-icon-triangle-1-s {
    background-position: -65px -16px; }
  .nbd-mode-vista .ui-tabs {
    position: relative;
    padding: .2em; }
  .nbd-mode-vista .ui-tabs .ui-tabs-nav {
    margin: 0;
    padding: .2em .2em 0; }
  .nbd-mode-vista .ui-tabs .ui-tabs-nav li {
    list-style: none;
    float: left;
    position: relative;
    top: 0;
    margin: 1px .2em 0 0;
    border-bottom-width: 0;
    padding: 0;
    white-space: nowrap; }
  .nbd-mode-vista .ui-tabs .ui-tabs-nav .ui-tabs-anchor {
    float: left;
    padding: .5em 1em;
    text-decoration: none; }
  .nbd-mode-vista .ui-tabs .ui-tabs-nav li.ui-tabs-active {
    margin-bottom: -1px;
    padding-bottom: 1px; }
  .nbd-mode-vista .ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor, .nbd-mode-vista .ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor, .nbd-mode-vista .ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
    cursor: text; }
  .nbd-mode-vista .ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor {
    cursor: pointer; }
  .nbd-mode-vista .ui-tabs .ui-tabs-panel {
    display: block;
    border-width: 0;
    padding: 1em 1.4em;
    background: none; }
  .nbd-mode-vista .ui-tooltip {
    padding: 8px;
    position: absolute;
    z-index: 9999;
    max-width: 300px;
    -webkit-box-shadow: 0 0 5px #aaa;
    box-shadow: 0 0 5px #aaa; }
  .nbd-mode-vista body .ui-tooltip {
    border-width: 2px; }
  .nbd-mode-vista .ui-widget {
    font-family: Verdana,Arial,sans-serif;
    font-size: 1.1em; }
  .nbd-mode-vista .ui-widget .ui-widget {
    font-size: 1em; }
  .nbd-mode-vista .ui-widget input, .nbd-mode-vista .ui-widget select, .nbd-mode-vista .ui-widget textarea, .nbd-mode-vista .ui-widget button {
    font-family: Verdana,Arial,sans-serif;
    font-size: 1em; }
  .nbd-mode-vista .ui-widget-content {
    border: 1px solid #aaa;
    background: #fff url(images/ui-bg_flat_75_ffffff_40x100.png) 50% 50% repeat-x;
    color: #222; }
  .nbd-mode-vista .ui-widget-content a {
    color: #222; }
  .nbd-mode-vista .ui-widget-header {
    border: 1px solid #aaa;
    background: #ccc url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
    color: #222;
    font-weight: bold; }
  .nbd-mode-vista .ui-widget-header a {
    color: #222; }
  .nbd-mode-vista .ui-state-default, .nbd-mode-vista .ui-widget-content .ui-state-default, .nbd-mode-vista .ui-widget-header .ui-state-default {
    border: 1px solid #d3d3d3;
    background: #e6e6e6 url(images/ui-bg_glass_75_e6e6e6_1x400.png) 50% 50% repeat-x;
    font-weight: normal;
    color: #555; }
  .nbd-mode-vista .ui-state-default a, .nbd-mode-vista .ui-state-default a:link, .nbd-mode-vista .ui-state-default a:visited {
    color: #555;
    text-decoration: none; }
  .nbd-mode-vista .ui-state-hover, .nbd-mode-vista .ui-widget-content .ui-state-hover, .nbd-mode-vista .ui-widget-header .ui-state-hover, .nbd-mode-vista .ui-state-focus, .nbd-mode-vista .ui-widget-content .ui-state-focus, .nbd-mode-vista .ui-widget-header .ui-state-focus {
    border: 1px solid #999;
    background: #dadada url(images/ui-bg_glass_75_dadada_1x400.png) 50% 50% repeat-x;
    font-weight: normal;
    color: #212121; }
  .nbd-mode-vista .ui-state-hover a, .nbd-mode-vista .ui-state-hover a:hover, .nbd-mode-vista .ui-state-hover a:link, .nbd-mode-vista .ui-state-hover a:visited, .nbd-mode-vista .ui-state-focus a, .nbd-mode-vista .ui-state-focus a:hover, .nbd-mode-vista .ui-state-focus a:link, .nbd-mode-vista .ui-state-focus a:visited {
    color: #212121;
    text-decoration: none; }
  .nbd-mode-vista .ui-state-active, .nbd-mode-vista .ui-widget-content .ui-state-active, .nbd-mode-vista .ui-widget-header .ui-state-active {
    border: 1px solid #aaa;
    background: #fff url(images/ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x;
    font-weight: normal;
    color: #212121; }
  .nbd-mode-vista .ui-state-active a, .nbd-mode-vista .ui-state-active a:link, .nbd-mode-vista .ui-state-active a:visited {
    color: #212121;
    text-decoration: none; }
  .nbd-mode-vista .ui-state-highlight, .nbd-mode-vista .ui-widget-content .ui-state-highlight, .nbd-mode-vista .ui-widget-header .ui-state-highlight {
    border: 1px solid #fcefa1;
    background: #fbf9ee url(images/ui-bg_glass_55_fbf9ee_1x400.png) 50% 50% repeat-x;
    color: #363636; }
  .nbd-mode-vista .ui-state-highlight a, .nbd-mode-vista .ui-widget-content .ui-state-highlight a, .nbd-mode-vista .ui-widget-header .ui-state-highlight a {
    color: #363636; }
  .nbd-mode-vista .ui-state-error, .nbd-mode-vista .ui-widget-content .ui-state-error, .nbd-mode-vista .ui-widget-header .ui-state-error {
    border: 1px solid #cd0a0a;
    background: #fef1ec url(images/ui-bg_glass_95_fef1ec_1x400.png) 50% 50% repeat-x;
    color: #cd0a0a; }
  .nbd-mode-vista .ui-state-error a, .nbd-mode-vista .ui-widget-content .ui-state-error a, .nbd-mode-vista .ui-widget-header .ui-state-error a {
    color: #cd0a0a; }
  .nbd-mode-vista .ui-state-error-text, .nbd-mode-vista .ui-widget-content .ui-state-error-text, .nbd-mode-vista .ui-widget-header .ui-state-error-text {
    color: #cd0a0a; }
  .nbd-mode-vista .ui-priority-primary, .nbd-mode-vista .ui-widget-content .ui-priority-primary, .nbd-mode-vista .ui-widget-header .ui-priority-primary {
    font-weight: bold; }
  .nbd-mode-vista .ui-priority-secondary, .nbd-mode-vista .ui-widget-content .ui-priority-secondary, .nbd-mode-vista .ui-widget-header .ui-priority-secondary {
    opacity: .7;
    filter: Alpha(Opacity=70);
    font-weight: normal; }
  .nbd-mode-vista .ui-state-disabled, .nbd-mode-vista .ui-widget-content .ui-state-disabled, .nbd-mode-vista .ui-widget-header .ui-state-disabled {
    opacity: .35;
    filter: Alpha(Opacity=35);
    background-image: none; }
  .nbd-mode-vista .ui-state-disabled .ui-icon {
    filter: Alpha(Opacity=35); }
  .nbd-mode-vista .ui-icon {
    width: 16px;
    height: 16px; }
  .nbd-mode-vista .ui-icon, .nbd-mode-vista .ui-widget-content .ui-icon {
    background-image: url(images/ui-icons_888888_256x240.png); }
  .nbd-mode-vista .ui-widget-header .ui-icon {
    background-image: url(images/ui-icons_888888_256x240.png); }
  .nbd-mode-vista .ui-state-default .ui-icon {
    background-image: url(images/ui-icons_888888_256x240.png); }
  .nbd-mode-vista .ui-state-hover .ui-icon, .nbd-mode-vista .ui-state-focus .ui-icon {
    background-image: url(images/ui-icons_454545_256x240.png); }
  .nbd-mode-vista .ui-state-active .ui-icon {
    background-image: url(images/ui-icons_454545_256x240.png); }
  .nbd-mode-vista .ui-state-highlight .ui-icon {
    background-image: url(images/ui-icons_2e83ff_256x240.png); }
  .nbd-mode-vista .ui-state-error .ui-icon, .nbd-mode-vista .ui-state-error-text .ui-icon {
    background-image: url(images/ui-icons_cd0a0a_256x240.png); }
  .nbd-mode-vista .ui-icon-blank {
    background-position: 16px 16px; }
  .nbd-mode-vista .ui-icon-carat-1-n {
    background-position: 0 0; }
  .nbd-mode-vista .ui-icon-carat-1-ne {
    background-position: -16px 0; }
  .nbd-mode-vista .ui-icon-carat-1-e {
    background-position: -32px 0; }
  .nbd-mode-vista .ui-icon-carat-1-se {
    background-position: -48px 0; }
  .nbd-mode-vista .ui-icon-carat-1-s {
    background-position: -64px 0; }
  .nbd-mode-vista .ui-icon-carat-1-sw {
    background-position: -80px 0; }
  .nbd-mode-vista .ui-icon-carat-1-w {
    background-position: -96px 0; }
  .nbd-mode-vista .ui-icon-carat-1-nw {
    background-position: -112px 0; }
  .nbd-mode-vista .ui-icon-carat-2-n-s {
    background-position: -128px 0; }
  .nbd-mode-vista .ui-icon-carat-2-e-w {
    background-position: -144px 0; }
  .nbd-mode-vista .ui-icon-triangle-1-n {
    background-position: 0 -16px; }
  .nbd-mode-vista .ui-icon-triangle-1-ne {
    background-position: -16px -16px; }
  .nbd-mode-vista .ui-icon-triangle-1-e {
    background-position: -32px -16px; }
  .nbd-mode-vista .ui-icon-triangle-1-se {
    background-position: -48px -16px; }
  .nbd-mode-vista .ui-icon-triangle-1-s {
    background-position: -64px -16px; }
  .nbd-mode-vista .ui-icon-triangle-1-sw {
    background-position: -80px -16px; }
  .nbd-mode-vista .ui-icon-triangle-1-w {
    background-position: -96px -16px; }
  .nbd-mode-vista .ui-icon-triangle-1-nw {
    background-position: -112px -16px; }
  .nbd-mode-vista .ui-icon-triangle-2-n-s {
    background-position: -128px -16px; }
  .nbd-mode-vista .ui-icon-triangle-2-e-w {
    background-position: -144px -16px; }
  .nbd-mode-vista .ui-icon-arrow-1-n {
    background-position: 0 -32px; }
  .nbd-mode-vista .ui-icon-arrow-1-ne {
    background-position: -16px -32px; }
  .nbd-mode-vista .ui-icon-arrow-1-e {
    background-position: -32px -32px; }
  .nbd-mode-vista .ui-icon-arrow-1-se {
    background-position: -48px -32px; }
  .nbd-mode-vista .ui-icon-arrow-1-s {
    background-position: -64px -32px; }
  .nbd-mode-vista .ui-icon-arrow-1-sw {
    background-position: -80px -32px; }
  .nbd-mode-vista .ui-icon-arrow-1-w {
    background-position: -96px -32px; }
  .nbd-mode-vista .ui-icon-arrow-1-nw {
    background-position: -112px -32px; }
  .nbd-mode-vista .ui-icon-arrow-2-n-s {
    background-position: -128px -32px; }
  .nbd-mode-vista .ui-icon-arrow-2-ne-sw {
    background-position: -144px -32px; }
  .nbd-mode-vista .ui-icon-arrow-2-e-w {
    background-position: -160px -32px; }
  .nbd-mode-vista .ui-icon-arrow-2-se-nw {
    background-position: -176px -32px; }
  .nbd-mode-vista .ui-icon-arrowstop-1-n {
    background-position: -192px -32px; }
  .nbd-mode-vista .ui-icon-arrowstop-1-e {
    background-position: -208px -32px; }
  .nbd-mode-vista .ui-icon-arrowstop-1-s {
    background-position: -224px -32px; }
  .nbd-mode-vista .ui-icon-arrowstop-1-w {
    background-position: -240px -32px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-n {
    background-position: 0 -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-ne {
    background-position: -16px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-e {
    background-position: -32px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-se {
    background-position: -48px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-s {
    background-position: -64px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-sw {
    background-position: -80px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-w {
    background-position: -96px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-1-nw {
    background-position: -112px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-2-n-s {
    background-position: -128px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-2-ne-sw {
    background-position: -144px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-2-e-w {
    background-position: -160px -48px; }
  .nbd-mode-vista .ui-icon-arrowthick-2-se-nw {
    background-position: -176px -48px; }
  .nbd-mode-vista .ui-icon-arrowthickstop-1-n {
    background-position: -192px -48px; }
  .nbd-mode-vista .ui-icon-arrowthickstop-1-e {
    background-position: -208px -48px; }
  .nbd-mode-vista .ui-icon-arrowthickstop-1-s {
    background-position: -224px -48px; }
  .nbd-mode-vista .ui-icon-arrowthickstop-1-w {
    background-position: -240px -48px; }
  .nbd-mode-vista .ui-icon-arrowreturnthick-1-w {
    background-position: 0 -64px; }
  .nbd-mode-vista .ui-icon-arrowreturnthick-1-n {
    background-position: -16px -64px; }
  .nbd-mode-vista .ui-icon-arrowreturnthick-1-e {
    background-position: -32px -64px; }
  .nbd-mode-vista .ui-icon-arrowreturnthick-1-s {
    background-position: -48px -64px; }
  .nbd-mode-vista .ui-icon-arrowreturn-1-w {
    background-position: -64px -64px; }
  .nbd-mode-vista .ui-icon-arrowreturn-1-n {
    background-position: -80px -64px; }
  .nbd-mode-vista .ui-icon-arrowreturn-1-e {
    background-position: -96px -64px; }
  .nbd-mode-vista .ui-icon-arrowreturn-1-s {
    background-position: -112px -64px; }
  .nbd-mode-vista .ui-icon-arrowrefresh-1-w {
    background-position: -128px -64px; }
  .nbd-mode-vista .ui-icon-arrowrefresh-1-n {
    background-position: -144px -64px; }
  .nbd-mode-vista .ui-icon-arrowrefresh-1-e {
    background-position: -160px -64px; }
  .nbd-mode-vista .ui-icon-arrowrefresh-1-s {
    background-position: -176px -64px; }
  .nbd-mode-vista .ui-icon-arrow-4 {
    background-position: 0 -80px; }
  .nbd-mode-vista .ui-icon-arrow-4-diag {
    background-position: -16px -80px; }
  .nbd-mode-vista .ui-icon-extlink {
    background-position: -32px -80px; }
  .nbd-mode-vista .ui-icon-newwin {
    background-position: -48px -80px; }
  .nbd-mode-vista .ui-icon-refresh {
    background-position: -64px -80px; }
  .nbd-mode-vista .ui-icon-shuffle {
    background-position: -80px -80px; }
  .nbd-mode-vista .ui-icon-transfer-e-w {
    background-position: -96px -80px; }
  .nbd-mode-vista .ui-icon-transferthick-e-w {
    background-position: -112px -80px; }
  .nbd-mode-vista .ui-icon-folder-collapsed {
    background-position: 0 -96px; }
  .nbd-mode-vista .ui-icon-folder-open {
    background-position: -16px -96px; }
  .nbd-mode-vista .ui-icon-document {
    background-position: -32px -96px; }
  .nbd-mode-vista .ui-icon-document-b {
    background-position: -48px -96px; }
  .nbd-mode-vista .ui-icon-note {
    background-position: -64px -96px; }
  .nbd-mode-vista .ui-icon-mail-closed {
    background-position: -80px -96px; }
  .nbd-mode-vista .ui-icon-mail-open {
    background-position: -96px -96px; }
  .nbd-mode-vista .ui-icon-suitcase {
    background-position: -112px -96px; }
  .nbd-mode-vista .ui-icon-comment {
    background-position: -128px -96px; }
  .nbd-mode-vista .ui-icon-person {
    background-position: -144px -96px; }
  .nbd-mode-vista .ui-icon-print {
    background-position: -160px -96px; }
  .nbd-mode-vista .ui-icon-trash {
    background-position: -176px -96px; }
  .nbd-mode-vista .ui-icon-locked {
    background-position: -192px -96px; }
  .nbd-mode-vista .ui-icon-unlocked {
    background-position: -208px -96px; }
  .nbd-mode-vista .ui-icon-bookmark {
    background-position: -224px -96px; }
  .nbd-mode-vista .ui-icon-tag {
    background-position: -240px -96px; }
  .nbd-mode-vista .ui-icon-home {
    background-position: 0 -112px; }
  .nbd-mode-vista .ui-icon-flag {
    background-position: -16px -112px; }
  .nbd-mode-vista .ui-icon-calendar {
    background-position: -32px -112px; }
  .nbd-mode-vista .ui-icon-cart {
    background-position: -48px -112px; }
  .nbd-mode-vista .ui-icon-pencil {
    background-position: -64px -112px; }
  .nbd-mode-vista .ui-icon-clock {
    background-position: -80px -112px; }
  .nbd-mode-vista .ui-icon-disk {
    background-position: -96px -112px; }
  .nbd-mode-vista .ui-icon-calculator {
    background-position: -112px -112px; }
  .nbd-mode-vista .ui-icon-zoomin {
    background-position: -128px -112px; }
  .nbd-mode-vista .ui-icon-zoomout {
    background-position: -144px -112px; }
  .nbd-mode-vista .ui-icon-search {
    background-position: -160px -112px; }
  .nbd-mode-vista .ui-icon-wrench {
    background-position: -176px -112px; }
  .nbd-mode-vista .ui-icon-gear {
    background-position: -192px -112px; }
  .nbd-mode-vista .ui-icon-heart {
    background-position: -208px -112px; }
  .nbd-mode-vista .ui-icon-star {
    background-position: -224px -112px; }
  .nbd-mode-vista .ui-icon-link {
    background-position: -240px -112px; }
  .nbd-mode-vista .ui-icon-cancel {
    background-position: 0 -128px; }
  .nbd-mode-vista .ui-icon-plus {
    background-position: -16px -128px; }
  .nbd-mode-vista .ui-icon-plusthick {
    background-position: -32px -128px; }
  .nbd-mode-vista .ui-icon-minus {
    background-position: -48px -128px; }
  .nbd-mode-vista .ui-icon-minusthick {
    background-position: -64px -128px; }
  .nbd-mode-vista .ui-icon-close {
    background-position: -80px -128px; }
  .nbd-mode-vista .ui-icon-closethick {
    background-position: -96px -128px; }
  .nbd-mode-vista .ui-icon-key {
    background-position: -112px -128px; }
  .nbd-mode-vista .ui-icon-lightbulb {
    background-position: -128px -128px; }
  .nbd-mode-vista .ui-icon-scissors {
    background-position: -144px -128px; }
  .nbd-mode-vista .ui-icon-clipboard {
    background-position: -160px -128px; }
  .nbd-mode-vista .ui-icon-copy {
    background-position: -176px -128px; }
  .nbd-mode-vista .ui-icon-contact {
    background-position: -192px -128px; }
  .nbd-mode-vista .ui-icon-image {
    background-position: -208px -128px; }
  .nbd-mode-vista .ui-icon-video {
    background-position: -224px -128px; }
  .nbd-mode-vista .ui-icon-script {
    background-position: -240px -128px; }
  .nbd-mode-vista .ui-icon-alert {
    background-position: 0 -144px; }
  .nbd-mode-vista .ui-icon-info {
    background-position: -16px -144px; }
  .nbd-mode-vista .ui-icon-notice {
    background-position: -32px -144px; }
  .nbd-mode-vista .ui-icon-help {
    background-position: -48px -144px; }
  .nbd-mode-vista .ui-icon-check {
    background-position: -64px -144px; }
  .nbd-mode-vista .ui-icon-bullet {
    background-position: -80px -144px; }
  .nbd-mode-vista .ui-icon-radio-on {
    background-position: -96px -144px; }
  .nbd-mode-vista .ui-icon-radio-off {
    background-position: -112px -144px; }
  .nbd-mode-vista .ui-icon-pin-w {
    background-position: -128px -144px; }
  .nbd-mode-vista .ui-icon-pin-s {
    background-position: -144px -144px; }
  .nbd-mode-vista .ui-icon-play {
    background-position: 0 -160px; }
  .nbd-mode-vista .ui-icon-pause {
    background-position: -16px -160px; }
  .nbd-mode-vista .ui-icon-seek-next {
    background-position: -32px -160px; }
  .nbd-mode-vista .ui-icon-seek-prev {
    background-position: -48px -160px; }
  .nbd-mode-vista .ui-icon-seek-end {
    background-position: -64px -160px; }
  .nbd-mode-vista .ui-icon-seek-start {
    background-position: -80px -160px; }
  .nbd-mode-vista .ui-icon-seek-first {
    background-position: -80px -160px; }
  .nbd-mode-vista .ui-icon-stop {
    background-position: -96px -160px; }
  .nbd-mode-vista .ui-icon-eject {
    background-position: -112px -160px; }
  .nbd-mode-vista .ui-icon-volume-off {
    background-position: -128px -160px; }
  .nbd-mode-vista .ui-icon-volume-on {
    background-position: -144px -160px; }
  .nbd-mode-vista .ui-icon-power {
    background-position: 0 -176px; }
  .nbd-mode-vista .ui-icon-signal-diag {
    background-position: -16px -176px; }
  .nbd-mode-vista .ui-icon-signal {
    background-position: -32px -176px; }
  .nbd-mode-vista .ui-icon-battery-0 {
    background-position: -48px -176px; }
  .nbd-mode-vista .ui-icon-battery-1 {
    background-position: -64px -176px; }
  .nbd-mode-vista .ui-icon-battery-2 {
    background-position: -80px -176px; }
  .nbd-mode-vista .ui-icon-battery-3 {
    background-position: -96px -176px; }
  .nbd-mode-vista .ui-icon-circle-plus {
    background-position: 0 -192px; }
  .nbd-mode-vista .ui-icon-circle-minus {
    background-position: -16px -192px; }
  .nbd-mode-vista .ui-icon-circle-close {
    background-position: -32px -192px; }
  .nbd-mode-vista .ui-icon-circle-triangle-e {
    background-position: -48px -192px; }
  .nbd-mode-vista .ui-icon-circle-triangle-s {
    background-position: -64px -192px; }
  .nbd-mode-vista .ui-icon-circle-triangle-w {
    background-position: -80px -192px; }
  .nbd-mode-vista .ui-icon-circle-triangle-n {
    background-position: -96px -192px; }
  .nbd-mode-vista .ui-icon-circle-arrow-e {
    background-position: -112px -192px; }
  .nbd-mode-vista .ui-icon-circle-arrow-s {
    background-position: -128px -192px; }
  .nbd-mode-vista .ui-icon-circle-arrow-w {
    background-position: -144px -192px; }
  .nbd-mode-vista .ui-icon-circle-arrow-n {
    background-position: -160px -192px; }
  .nbd-mode-vista .ui-icon-circle-zoomin {
    background-position: -176px -192px; }
  .nbd-mode-vista .ui-icon-circle-zoomout {
    background-position: -192px -192px; }
  .nbd-mode-vista .ui-icon-circle-check {
    background-position: -208px -192px; }
  .nbd-mode-vista .ui-icon-circlesmall-plus {
    background-position: 0 -208px; }
  .nbd-mode-vista .ui-icon-circlesmall-minus {
    background-position: -16px -208px; }
  .nbd-mode-vista .ui-icon-circlesmall-close {
    background-position: -32px -208px; }
  .nbd-mode-vista .ui-icon-squaresmall-plus {
    background-position: -48px -208px; }
  .nbd-mode-vista .ui-icon-squaresmall-minus {
    background-position: -64px -208px; }
  .nbd-mode-vista .ui-icon-squaresmall-close {
    background-position: -80px -208px; }
  .nbd-mode-vista .ui-icon-grip-dotted-vertical {
    background-position: 0 -224px; }
  .nbd-mode-vista .ui-icon-grip-dotted-horizontal {
    background-position: -16px -224px; }
  .nbd-mode-vista .ui-icon-grip-solid-vertical {
    background-position: -32px -224px; }
  .nbd-mode-vista .ui-icon-grip-solid-horizontal {
    background-position: -48px -224px; }
  .nbd-mode-vista .ui-icon-gripsmall-diagonal-se {
    background-position: -64px -224px; }
  .nbd-mode-vista .ui-icon-grip-diagonal-se {
    background-position: -80px -224px; }
  .nbd-mode-vista .ui-corner-all, .nbd-mode-vista .ui-corner-top, .nbd-mode-vista .ui-corner-left, .nbd-mode-vista .ui-corner-tl {
    border-top-left-radius: 4px; }
  .nbd-mode-vista .ui-corner-all, .nbd-mode-vista .ui-corner-top, .nbd-mode-vista .ui-corner-right, .nbd-mode-vista .ui-corner-tr {
    border-top-right-radius: 4px; }
  .nbd-mode-vista .ui-corner-all, .nbd-mode-vista .ui-corner-bottom, .nbd-mode-vista .ui-corner-left, .nbd-mode-vista .ui-corner-bl {
    border-bottom-left-radius: 4px; }
  .nbd-mode-vista .ui-corner-all, .nbd-mode-vista .ui-corner-bottom, .nbd-mode-vista .ui-corner-right, .nbd-mode-vista .ui-corner-br {
    border-bottom-right-radius: 4px; }
  .nbd-mode-vista .ui-widget-overlay {
    background: #aaa url(images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x;
    opacity: .3;
    filter: Alpha(Opacity=30); }
  .nbd-mode-vista .ui-widget-shadow {
    margin: -8px 0 0 -8px;
    padding: 8px;
    background: #aaa url(images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x;
    opacity: .3;
    filter: Alpha(Opacity=30);
    border-radius: 8px; }
  .nbd-mode-vista .nbd-icon-vista {
    font-size: 24px;
    color: #04b591;
    line-height: normal;
    display: inline-block;
    cursor: pointer; }
  .nbd-mode-vista a, .nbd-mode-vista span, .nbd-mode-vista div, .nbd-mode-vista p {
    font-size: 14px;
    color: #666; }
  .nbd-mode-vista a {
    color: #666; }
  .nbd-mode-vista img {
    max-width: 100%; }
  .nbd-mode-vista strong {
    font-weight: bold; }
  .nbd-mode-vista input, .nbd-mode-vista input:focus, .nbd-mode-vista input:active, .nbd-mode-vista input:hover {
    border-color: transparent !important;
    outline: none !important; }
  .nbd-mode-vista input[type=email], .nbd-mode-vista input[type=password], .nbd-mode-vista input[type=search], .nbd-mode-vista input[type=text], .nbd-mode-vista input[type=url], .nbd-mode-vista textarea {
    box-shadow: none;
    border-radius: 2px;
    background-color: #ccc;
    padding: 5px;
    font-size: 14px; }
  .nbd-mode-vista .text-center {
    text-align: center; }
  .nbd-mode-vista .text-capital {
    text-transform: capitalize !important; }
  .nbd-mode-vista .nbd-shadow {
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.12), 0 4px 4px rgba(0, 0, 0, 0.24); }
  .nbd-mode-vista .nbd-d-flex {
    display: flex; }
  .nbd-mode-vista .nbd-justify-content-start {
    justify-content: flex-start !important; }
  .nbd-mode-vista .nbd-disabled {
    pointer-events: none;
    opacity: 0.3; }
  .nbd-mode-vista .disabled {
    pointer-events: none;
    opacity: 0.3; }
  .nbd-mode-vista .nbd-enable {
    pointer-events: unset;
    opacity: 1; }
  .nbd-mode-vista .rotate-45 {
    display: inline-block;
    transform: rotate(-45deg); }
  .nbd-mode-vista .rotate45 {
    display: inline-block;
    transform: rotate(45deg); }
  .nbd-mode-vista .rotate30 {
    display: inline-block;
    transform: rotate(30deg); }
  .nbd-mode-vista .rotate-30 {
    display: inline-block;
    transform: rotate(-30deg); }
  .nbd-mode-vista .rotate-60 {
    display: inline-block;
    transform: rotate(-60deg); }
  .nbd-mode-vista .rotate60 {
    display: inline-block;
    transform: rotate(60deg); }
  .nbd-mode-vista .rotate90 {
    display: inline-block;
    transform: rotate(90deg); }
  .nbd-mode-vista .rotate-90 {
    display: inline-block;
    transform: rotate(-90deg); }
  .nbd-mode-vista .rotate135 {
    display: inline-block;
    transform: rotate(135deg); }
  .nbd-mode-vista .rotate-135 {
    display: inline-block;
    transform: rotate(-135deg); }
  .nbd-mode-vista .rotate-180 {
    display: inline-block;
    transform: rotate(-180deg); }
  .nbd-mode-vista .rotate180 {
    display: inline-block;
    transform: rotate(180deg); }
  .nbd-mode-vista .animate100 {
    animation-duration: 0.1s !important; }
  .nbd-mode-vista .animate200 {
    animation-duration: 0.2s !important; }
  .nbd-mode-vista .animate300 {
    animation-duration: 0.3s !important; }
  .nbd-mode-vista .animate400 {
    animation-duration: 0.4s !important; }
  .nbd-mode-vista .animate500 {
    animation-duration: 0.5s !important; }
  .nbd-mode-vista .animate600 {
    animation-duration: 0.6s !important; }
  .nbd-mode-vista .animate700 {
    animation-duration: 0.7s !important; }
  .nbd-mode-vista .animate800 {
    animation-duration: 0.8s !important; }
  .nbd-mode-vista .animate900 {
    animation-duration: 0.9s !important; }
  .nbd-mode-vista .animate1000 {
    animation-duration: 1s !important; }
  .nbd-mode-vista .animate1100 {
    animation-duration: 1.1s !important; }
  .nbd-mode-vista .v-toolbar {
    margin-bottom: 30px; }
    .nbd-mode-vista .v-toolbar .main-toolbar {
      display: flex;
      justify-content: space-between;
      border-radius: 2px;
      position: relative;
      z-index: 1;
      background-color: #f4f4f4; }
      .nbd-mode-vista .v-toolbar .main-toolbar .v-tab span {
        line-height: 26px; }
      @media screen and (max-width: 692px) {
        .nbd-mode-vista .v-toolbar .main-toolbar .v-toolbar-item.left-toolbar {
          margin: auto; }
        .nbd-mode-vista .v-toolbar .main-toolbar .v-toolbar-item.right-toolbar {
          margin: 15px auto 0; } }
    .nbd-mode-vista .v-toolbar .left-toolbar .tabs-toolbar {
      position: relative; }
    .nbd-mode-vista .v-toolbar #selectedTab {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 0;
      height: 46px;
      background: #04b591;
      transition: left .4s ease-out, width .4s ease-out; }
  .nbd-mode-vista .v-main-menu {
    display: flex;
    height: 100%; }
    .nbd-mode-vista .v-main-menu .v-menu-item {
      padding: 10px 25px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all .3s; }
      .nbd-mode-vista .v-main-menu .v-menu-item:last-child:after {
        display: none; }
      .nbd-mode-vista .v-main-menu .v-menu-item:hover {
        background-color: rgba(0, 0, 0, 0.1); }
      .nbd-mode-vista .v-main-menu .v-menu-item.active {
        background-color: #04b591; }
        .nbd-mode-vista .v-main-menu .v-menu-item.active span {
          color: #fff; }
      .nbd-mode-vista .v-main-menu .v-menu-item i {
        margin-right: 5px;
        color: #04b591; }
  .nbd-mode-vista .v-tab-contents {
    position: relative;
    overflow: hidden; }
    .nbd-mode-vista .v-tab-contents .v-tab-content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      visibility: hidden;
      opacity: 0;
      transition: all .3s; }
      .nbd-mode-vista .v-tab-contents .v-tab-content.active {
        visibility: visible;
        opacity: 1; }
  .nbd-mode-vista .tab-scroll {
    position: relative;
    overflow: hidden;
    height: 100%; }
    .nbd-mode-vista .tab-scroll > .main-scrollbar {
      margin-bottom: 10px;
      padding: 10px; }
  .nbd-mode-vista .v-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: -1;
    opacity: 0;
    visibility: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all .3s; }
    .nbd-mode-vista .v-popup .main-popup {
      pointer-events: all;
      background-color: #fff;
      border-radius: 2px;
      box-shadow: 0 0 42px rgba(0, 0, 0, 0.15);
      box-sizing: border-box;
      padding: 20px;
      text-align: left;
      width: 525px;
      transition: all .6s;
      position: relative; }
    .nbd-mode-vista .v-popup.nb-show {
      opacity: 1;
      visibility: visible;
      z-index: 9999999; }
    .nbd-mode-vista .v-popup[data-animate="scale"] .main-popup {
      transform: scale(0.8);
      transition: all .3s; }
    .nbd-mode-vista .v-popup[data-animate="scale"].nb-show .main-popup {
      transform: scale(1); }
    .nbd-mode-vista .v-popup[data-animate="bottom-to-top"] .main-popup {
      transform: translate(0, 50%);
      transition: all .3s; }
    .nbd-mode-vista .v-popup[data-animate="bottom-to-top"].nb-show .main-popup {
      transform: translate(0, 0); }
    .nbd-mode-vista .v-popup[data-animate="top-to-bottom"] .main-popup {
      transform: translate(0, -50%);
      transition: all .3s; }
    .nbd-mode-vista .v-popup[data-animate="top-to-bottom"].nb-show .main-popup {
      transform: translate(0, 0); }
    .nbd-mode-vista .v-popup[data-animate="left-to-right"] .main-popup {
      transform: translate(-50%, 0);
      transition: all .3s; }
    .nbd-mode-vista .v-popup[data-animate="left-to-right"].nb-show .main-popup {
      transform: translate(0, 0); }
    .nbd-mode-vista .v-popup[data-animate="right-to-left"] .main-popup {
      transform: translate(50%, 0);
      transition: all .3s; }
    .nbd-mode-vista .v-popup[data-animate="right-to-left"].nb-show .main-popup {
      transform: translate(0, 0); }
    .nbd-mode-vista .v-popup[data-animate="fixed-top"] {
      align-items: flex-start; }
      .nbd-mode-vista .v-popup[data-animate="fixed-top"] .main-popup {
        margin-top: 60px;
        transform: translate(0, -50%);
        transition: all .3s; }
      .nbd-mode-vista .v-popup[data-animate="fixed-top"].nb-show .main-popup {
        transform: translate(0, 0); }
    .nbd-mode-vista .v-popup .close-popup {
      position: absolute;
      top: 10px;
      right: 10px;
      font-size: 24px;
      cursor: pointer; }
    .nbd-mode-vista .v-popup .overlay-popup {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%; }
    .nbd-mode-vista .v-popup .overlay-main {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: white;
      z-index: -1;
      opacity: 0;
      visibility: hidden;
      transition: all .4s; }
      .nbd-mode-vista .v-popup .overlay-main.active {
        z-index: 99;
        opacity: 1;
        visibility: visible;
        transition: unset; }
    .nbd-mode-vista .v-popup.popup-share .main-popup .head {
      text-align: center; }
      .nbd-mode-vista .v-popup.popup-share .main-popup .head h2 {
        font-size: 21px;
        border-bottom: 1px solid #ebebeb;
        margin-bottom: 0;
        padding-bottom: 20px; }
    .nbd-mode-vista .v-popup.popup-share .main-popup .body {
      padding-top: 20px; }
      .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 20px; }
        .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with span {
          margin-right: 20px; }
        .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with ul.socials {
          list-style: none;
          display: flex;
          justify-content: space-around;
          align-items: center; }
          .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with ul.socials li.social {
            margin: 0 15px; }
            .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with ul.socials li.social i {
              font-size: 45px;
              border-radius: 50%;
              cursor: pointer; }
            .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with ul.socials li.social.facebook i {
              color: #3b5998; }
            .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with ul.socials li.social.twitter i {
              color: #00aced; }
            .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-with ul.socials li.social.google-plus i {
              color: #d34836; }
      .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-content textarea {
        border: solid 1px #ebebeb;
        border-radius: 2px;
        min-height: 78px;
        resize: none;
        padding: 10px 15px;
        box-sizing: border-box;
        outline: none;
        width: 100%; }
        .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-content textarea:focus {
          border-color: #ccc; }
      .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-btn {
        margin-top: 20px; }
        .nbd-mode-vista .v-popup.popup-share .main-popup .body .share-btn .nbd-button {
          border-radius: 2px;
          margin-left: 0;
          margin-right: 0; }
    .nbd-mode-vista .v-popup.v-popup-webcam .main-popup {
      width: 80%;
      min-height: 500px;
      display: flex; }
    .nbd-mode-vista .v-popup.v-popup-webcam .footer {
      align-self: flex-end; }
    .nbd-mode-vista .v-popup.popup-keyboard .main-popup .head {
      padding-bottom: 10px;
      border-bottom: 1px solid #ebebeb; }
    .nbd-mode-vista .v-popup.popup-keyboard .main-popup .body {
      margin-top: 20px; }
      .nbd-mode-vista .v-popup.popup-keyboard .main-popup .body table tr td {
        padding: 3px 0;
        font-size: 12px; }
        .nbd-mode-vista .v-popup.popup-keyboard .main-popup .body table tr td.keys {
          padding-right: 10px;
          text-align: right; }
          .nbd-mode-vista .v-popup.popup-keyboard .main-popup .body table tr td.keys kbd {
            display: inline-block;
            padding-left: 2px;
            height: 20px;
            border: 1px solid #ddd;
            text-align: center;
            line-height: 20px;
            padding-right: 2px;
            border-radius: 3px;
            color: #242729;
            font-size: 11px;
            margin-left: 5px;
            min-width: 22px;
            box-shadow: 0 1px 0 rgba(12, 13, 14, 0.2), 0 0 0 2px #FFF inset;
            background-color: #e1e3e5; }
    .nbd-mode-vista .v-popup.popup-select {
      background-color: transparent; }
      .nbd-mode-vista .v-popup.popup-select .close-popup, .nbd-mode-vista .v-popup.popup-select i {
        color: #fff; }
        .nbd-mode-vista .v-popup.popup-select .close-popup:hover, .nbd-mode-vista .v-popup.popup-select i:hover {
          color: #fff; }
      .nbd-mode-vista .v-popup.popup-select .main-popup {
        width: auto;
        padding: 0;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.15); }
        .nbd-mode-vista .v-popup.popup-select .main-popup .head {
          font-size: 21px;
          letter-spacing: .005em;
          box-sizing: border-box;
          font-weight: 400;
          width: 100%;
          padding: 15px 20px;
          margin: 0;
          color: #fff;
          background-color: #04b591; }
        .nbd-mode-vista .v-popup.popup-select .main-popup .body {
          padding: 20px; }
          .nbd-mode-vista .v-popup.popup-select .main-popup .body .title {
            padding-bottom: 18px;
            display: block;
            max-width: 250px; }
          .nbd-mode-vista .v-popup.popup-select .main-popup .body .main-select {
            display: flex;
            justify-content: space-between; }
            .nbd-mode-vista .v-popup.popup-select .main-popup .body .main-select .nbd-button {
              margin: 0;
              display: flex;
              justify-content: space-between; }
              .nbd-mode-vista .v-popup.popup-select .main-popup .body .main-select .nbd-button i {
                font-size: 18px; }
              .nbd-mode-vista .v-popup.popup-select .main-popup .body .main-select .nbd-button.select-no {
                background-color: #787d92; }
    .nbd-mode-vista .v-popup.v-popup-terms .head {
      margin-bottom: 20px; }
    .nbd-mode-vista .v-popup.v-popup-select {
      background-color: transparent; }
      .nbd-mode-vista .v-popup.v-popup-select .close-popup, .nbd-mode-vista .v-popup.v-popup-select i {
        color: #fff; }
        .nbd-mode-vista .v-popup.v-popup-select .close-popup:hover, .nbd-mode-vista .v-popup.v-popup-select i:hover {
          color: #fff; }
      .nbd-mode-vista .v-popup.v-popup-select .main-popup {
        width: auto;
        padding: 0;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.15); }
        .nbd-mode-vista .v-popup.v-popup-select .main-popup .head {
          font-size: 21px;
          letter-spacing: .005em;
          box-sizing: border-box;
          font-weight: 400;
          width: 100%;
          padding: 15px 20px;
          margin: 0;
          color: #fff;
          background-color: #04b591; }
        .nbd-mode-vista .v-popup.v-popup-select .main-popup .body {
          padding: 20px; }
          .nbd-mode-vista .v-popup.v-popup-select .main-popup .body .title {
            padding-bottom: 18px;
            display: block;
            max-width: 250px; }
          .nbd-mode-vista .v-popup.v-popup-select .main-popup .body .main-select {
            display: flex;
            justify-content: space-between; }
            .nbd-mode-vista .v-popup.v-popup-select .main-popup .body .main-select .v-btn {
              margin: 0;
              display: flex;
              justify-content: space-between;
              align-items: center; }
              .nbd-mode-vista .v-popup.v-popup-select .main-popup .body .main-select .v-btn i {
                font-size: 18px;
                margin-right: 15px; }
              .nbd-mode-vista .v-popup.v-popup-select .main-popup .body .main-select .v-btn.select-no {
                background-color: #787d92; }
  .nbd-mode-vista .nbd-context-menu {
    position: fixed;
    top: 95px !important;
    left: 500px !important;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 3px 4px 0 rgba(0, 0, 0, 0.14);
    z-index: 99;
    visibility: visible; }
    .nbd-mode-vista .nbd-context-menu .main-context .contexts {
      list-style-type: none;
      display: inline-block;
      margin: 10px 0; }
      .nbd-mode-vista .nbd-context-menu .main-context .contexts .context-item {
        width: 100%;
        padding: 0 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 13px;
        line-height: 36px;
        cursor: pointer; }
        .nbd-mode-vista .nbd-context-menu .main-context .contexts .context-item:hover {
          background-color: #f6f6f6; }
        .nbd-mode-vista .nbd-context-menu .main-context .contexts .context-item i {
          font-size: 21px;
          display: inline-block;
          margin-right: 15px; }
      .nbd-mode-vista .nbd-context-menu .main-context .contexts .separator {
        margin-top: 4px;
        margin-bottom: 4px;
        height: 1px;
        min-height: 1px;
        max-height: 1px;
        width: 100%;
        background-color: #ebebeb; }
  .nbd-mode-vista .nbd-warning {
    position: absolute;
    top: 10px;
    right: 10px;
    min-width: 100px;
    max-width: 350px;
    border-radius: 2px;
    background-color: transparent;
    visibility: hidden;
    opacity: 0;
    z-index: -1; }
    .nbd-mode-vista .nbd-warning.nbd-show {
      visibility: visible;
      opacity: 1;
      z-index: 9999999; }
    .nbd-mode-vista .nbd-warning .main-warning {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
      background-color: #fff;
      box-shadow: 1px 0 10px rgba(0, 0, 0, 0.08);
      padding: 5px; }
      .nbd-mode-vista .nbd-warning .main-warning i {
        padding: 5px;
        font-size: 21px; }
      .nbd-mode-vista .nbd-warning .main-warning .warning {
        color: #de9309; }
      .nbd-mode-vista .nbd-warning .main-warning .title-warning {
        padding: 0 5px;
        font-size: 12px;
        margin-right: auto; }
      .nbd-mode-vista .nbd-warning .main-warning .close-warning {
        cursor: pointer; }
  .nbd-mode-vista .nbd-tool-lock {
    position: absolute;
    top: 10px;
    right: 10px; }
    .nbd-mode-vista .nbd-tool-lock .main-tool-lock {
      padding: 10px; }
    .nbd-mode-vista .nbd-tool-lock ul {
      list-style: none; }
    .nbd-mode-vista .nbd-tool-lock .items-lock {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column; }
      .nbd-mode-vista .nbd-tool-lock .items-lock .item-lock {
        padding: 10px;
        background: #fff;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 5px;
        cursor: pointer;
        border-radius: 2px;
        box-shadow: 1px 0 10px rgba(0, 0, 0, 0.08); }
        .nbd-mode-vista .nbd-tool-lock .items-lock .item-lock.active i {
          color: #ef5350; }
        .nbd-mode-vista .nbd-tool-lock .items-lock .item-lock:last-child {
          margin-bottom: 0; }
        .nbd-mode-vista .nbd-tool-lock .items-lock .item-lock i {
          font-size: 14px; }
          .nbd-mode-vista .nbd-tool-lock .items-lock .item-lock i.horizontal {
            position: relative; }
            .nbd-mode-vista .nbd-tool-lock .items-lock .item-lock i.horizontal sub {
              position: absolute;
              top: 5px;
              left: 10px;
              font-size: 10px; }
  .nbd-mode-vista .nbd-toasts {
    position: fixed;
    bottom: 0;
    left: 20px;
    color: #fff;
    border-radius: 2px;
    visibility: hidden;
    opacity: 0;
    z-index: -1;
    transition: all .3s;
    min-width: 250px; }
    .nbd-mode-vista .nbd-toasts.nbd-show {
      opacity: 1;
      visibility: visible;
      z-index: 99999999;
      bottom: 10px; }
    .nbd-mode-vista .nbd-toasts .toast {
      margin-bottom: 10px;
      background: #04b591;
      padding: 10px 45px 10px 20px;
      position: relative; }
      .nbd-mode-vista .nbd-toasts .toast span {
        color: #fff; }
      .nbd-mode-vista .nbd-toasts .toast i {
        color: #fff;
        font-size: 21px;
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        cursor: pointer; }
  .nbd-mode-vista .nbd-load-page {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99999999999;
    background: #fdfdfd; }
    .nbd-mode-vista .nbd-load-page .loader {
      position: relative;
      margin: -50px auto 0 -50px;
      width: 100px;
      top: 50%;
      left: 50%; }
      .nbd-mode-vista .nbd-load-page .loader:before {
        content: '';
        display: block;
        padding-top: 100%; }
  .nbd-mode-vista .circular {
    -webkit-animation: rotate 2s linear infinite;
    -moz-animation: rotate 2s linear infinite;
    -ms-animation: rotate 2s linear infinite;
    animation: rotate 2s linear infinite;
    height: 100%;
    -webkit-transform-origin: center center;
    transform-origin: center center;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto; }
    .nbd-mode-vista .circular .path {
      stroke-dasharray: 1,200;
      stroke-dashoffset: 0;
      -webkit-animation: dash 1.5s ease-in-out infinite,color 6s ease-in-out infinite;
      -moz-animation: dash 1.5s ease-in-out infinite,color 6s ease-in-out infinite;
      -ms-animation: dash 1.5s ease-in-out infinite,color 6s ease-in-out infinite;
      animation: dash 1.5s ease-in-out infinite,color 6s ease-in-out infinite;
      stroke-linecap: round; }

@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg); } }

@keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-webkit-keyframes dash {
  0% {
    stroke-dasharray: 1,200;
    stroke-dashoffset: 0; }
  50% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -35px; }
  100% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -124px; } }

@keyframes dash {
  0% {
    stroke-dasharray: 1,200;
    stroke-dashoffset: 0; }
  50% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -35px; }
  100% {
    stroke-dasharray: 89,200;
    stroke-dashoffset: -124px; } }

@-webkit-keyframes color {
  0%, 100% {
    stroke: #d62d20; }
  40% {
    stroke: #0057e7; }
  66% {
    stroke: #008744; }
  80%, 90% {
    stroke: #ffa700; } }

@keyframes color {
  0%, 100% {
    stroke: #d62d20; }
  40% {
    stroke: #0057e7; }
  66% {
    stroke: #008744; }
  80%, 90% {
    stroke: #ffa700; } }
  .nbd-mode-vista .nbd-text-color-picker {
    position: absolute;
    left: 40px;
    top: 50px;
    -webkit-transform: scale(0.8);
    -ms-transform: scale(0.8);
    transform: scale(0.8);
    visibility: hidden;
    opacity: 0;
    transition: all .3s;
    box-shadow: 1px 0 15px rgba(0, 0, 0, 0.2);
    background-color: #fff;
    overflow: hidden; }
    .nbd-mode-vista .nbd-text-color-picker .sp-container {
      z-index: -1; }
    .nbd-mode-vista .nbd-text-color-picker.active {
      opacity: 1;
      visibility: visible;
      -webkit-transform: scale(1);
      -ms-transform: scale(1);
      transform: scale(1); }
      .nbd-mode-vista .nbd-text-color-picker.active .sp-container {
        z-index: 99; }
    .nbd-mode-vista .nbd-text-color-picker .v-btn {
      margin-top: 0;
      margin-left: 10px;
      margin-bottom: 10px; }
  .nbd-mode-vista .nbd-sp.sp-container {
    background-color: #fff;
    border: solid 1px transparent; }
    .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container {
      border-left: none; }
      .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-color, .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-hue {
        cursor: crosshair; }
      .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-dragger {
        width: 7px;
        height: 7px; }
      .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-input {
        border: 1px solid #ebebeb; }
        .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-input:focus, .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-input:active {
          border-color: #ccc; }
      .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-choose {
        color: #fff;
        background-image: none;
        background-color: #04b591;
        border: none;
        border-radius: 2px;
        transition: box-shadow .3s;
        padding: 10px 15px; }
        .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-choose:hover {
          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12); }
      .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-color, .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-hue, .nbd-mode-vista .nbd-sp.sp-container .sp-picker-container .sp-clear {
        border: none; }
  .nbd-mode-vista .loading-contain {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    z-index: -1;
    transition: all .6s; }
    .nbd-mode-vista .loading-contain.nbd-show {
      visibility: visible;
      opacity: 1;
      z-index: 9999; }
    .nbd-mode-vista .loading-contain .circular {
      width: 80px;
      height: 80px; }
    .nbd-mode-vista .loading-contain.loading-app {
      background: white; }
  .nbd-mode-vista .nbd-toolbar-zoom {
    position: absolute;
    bottom: 10px;
    right: 30px;
    z-index: 99; }
    .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer {
      border-radius: 60px;
      background-color: #fff;
      box-shadow: 1px 0 10px rgba(0, 0, 0, 0.08);
      border: none;
      display: flex;
      justify-content: center;
      align-items: center; }
      .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item {
        padding: 5px;
        display: flex;
        align-items: center;
        cursor: pointer; }
        .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item i {
          font-size: 24px; }
        .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-fullscreen {
          border-right: 1px solid #ebebeb; }
        .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level {
          position: relative; }
          .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level span {
            font-size: 14px;
            font-weight: 500; }
          .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover {
            min-width: auto;
            top: -15px;
            left: 50%;
            transform: translate(-50%, -100%);
            box-shadow: 1px 0 10px rgba(0, 0, 0, 0.08);
            background-color: #fff; }
            .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover:after, .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover:before {
              transform: translateX(-50%) rotate(-180deg);
              bottom: -14px;
              top: auto; }
            .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-list {
              margin: 10px 0; }
            .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item {
              font-size: 13px;
              padding: 5px 40px 5px 15px;
              cursor: pointer; }
              .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item.active {
                position: relative; }
                .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item.active:before {
                  font-family: nbd-vista !important;
                  content: "\E911";
                  position: absolute;
                  top: 4px;
                  right: 10px;
                  speak: none;
                  font-style: normal;
                  font-weight: 400;
                  font-variant: normal;
                  text-transform: none;
                  line-height: 1;
                  -webkit-font-smoothing: antialiased;
                  font-size: 18px;
                  color: #04b591; }
              .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item:hover {
                background-color: #f6f6f6; }
  .nbd-mode-vista .v-btn {
    text-decoration: none;
    font-size: 14px;
    color: #fff;
    background-color: #04b591;
    text-align: center;
    letter-spacing: .5px;
    -webkit-transition: background-color .2s ease-out;
    transition: background-color .2s ease-out;
    cursor: pointer;
    outline: 0;
    border: none;
    border-radius: 2px;
    display: inline-block;
    height: 36px;
    line-height: 36px;
    padding: 0 16px;
    text-transform: uppercase;
    vertical-align: middle;
    -webkit-tap-highlight-color: transparent;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.12), 0 1px 5px 0 rgba(0, 0, 0, 0.2); }
    .nbd-mode-vista .v-btn:hover {
      background-color: #07d5ab;
      box-shadow: 0 3px 3px 0 rgba(0, 0, 0, 0.14), 0 1px 7px 0 rgba(0, 0, 0, 0.12), 0 3px 1px -1px rgba(0, 0, 0, 0.2); }
    .nbd-mode-vista .v-btn.waves-effect {
      position: relative;
      cursor: pointer;
      display: inline-block;
      overflow: hidden;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      -webkit-tap-highlight-color: transparent;
      vertical-align: middle;
      z-index: 1;
      -webkit-transition: .3s ease-out;
      transition: .3s ease-out; }
  .nbd-mode-vista .v-dropdown {
    position: relative; }
    .nbd-mode-vista .v-dropdown .v-dropdown-menu {
      position: absolute;
      opacity: 0;
      visibility: hidden;
      z-index: 9999; }
    .nbd-mode-vista .v-dropdown.active .v-dropdown-menu {
      opacity: 1;
      visibility: visible; }
  .nbd-mode-vista .nbd-checkbox {
    position: relative;
    height: 20px;
    display: inline-block; }
    .nbd-mode-vista .nbd-checkbox input[type="checkbox"] {
      outline: 0;
      margin-right: 10px; }
      .nbd-mode-vista .nbd-checkbox input[type="checkbox"]:checked + label:before {
        background: #394264;
        border: none; }
      .nbd-mode-vista .nbd-checkbox input[type="checkbox"]:checked + label:after {
        transform: rotate(-45deg);
        top: 5px;
        left: 4px;
        width: 12px;
        height: 6px;
        border: 2px solid #fff;
        border-top-style: none;
        border-right-style: none; }
    .nbd-mode-vista .nbd-checkbox label {
      cursor: pointer;
      display: inline-block;
      margin-bottom: 5px;
      font-weight: bold; }
      .nbd-mode-vista .nbd-checkbox label:before, .nbd-mode-vista .nbd-checkbox label:after {
        content: "";
        position: absolute;
        left: 0;
        top: 0; }
      .nbd-mode-vista .nbd-checkbox label:before {
        width: 20px;
        height: 20px;
        background: #fff;
        border: 2px solid rgba(0, 0, 0, 0.54);
        border-radius: 2px;
        cursor: pointer;
        transition: background .3s;
        box-sizing: border-box; }
  .nbd-mode-vista .v-ranges {
    display: table; }
    .nbd-mode-vista .v-ranges .range {
      display: table-row;
      height: 30px; }
      .nbd-mode-vista .v-ranges .range label {
        display: table-cell;
        padding-right: 10px;
        text-align: right;
        text-transform: capitalize;
        white-space: nowrap;
        font-weight: normal;
        font-size: 12px; }
      .nbd-mode-vista .v-ranges .range .main-track {
        display: table-cell;
        position: relative;
        width: 100%; }
        .nbd-mode-vista .v-ranges .range .main-track .slide-input {
          background-color: transparent;
          border: none;
          height: 7px;
          outline: none;
          padding: 0;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          z-index: 100;
          margin: 4px 0 0;
          -webkit-appearance: none; }
          .nbd-mode-vista .v-ranges .range .main-track .slide-input::-webkit-slider-thumb {
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #04b591;
            cursor: pointer; }
          .nbd-mode-vista .v-ranges .range .main-track .slide-input::-moz-range-thumb {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #04b591;
            cursor: pointer; }
        .nbd-mode-vista .v-ranges .range .main-track .range-track {
          background-color: #aaa;
          height: 2px;
          position: absolute;
          top: 7px;
          left: 0;
          width: 100%; }
        .nbd-mode-vista .v-ranges .range .main-track .snap-guide {
          position: absolute;
          border-left: 2px solid #aaa;
          left: 50%;
          top: 0;
          height: 12px;
          margin-top: 2px;
          z-index: 90; }
      .nbd-mode-vista .v-ranges .range .value-display {
        display: table-cell;
        text-align: right;
        padding-left: 10px;
        min-width: 31px;
        font-size: 12px; }
    .nbd-mode-vista .v-ranges .main-track input {
      padding: 0; }
  .nbd-mode-vista .v-range-model {
    min-width: 25px; }
  .nbd-mode-vista .nbd-tab-contents .nbd-tab-content {
    display: none; }
    .nbd-mode-vista .nbd-tab-contents .nbd-tab-content.active {
      display: block; }
  .nbd-mode-vista .nbd-color-palette {
    min-width: 227px;
    position: absolute;
    right: 0;
    top: calc(100%);
    background-color: #fff;
    display: none;
    border: 1px solid #ebebeb; }
    .nbd-mode-vista .nbd-color-palette.show {
      display: block; }
    .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner {
      padding: 10px;
      position: relative; }
      .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner:before {
        border: 7px solid transparent;
        border-bottom: 7px solid #ebebeb;
        content: "";
        position: absolute;
        top: -15px;
        display: none; }
      .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner:after {
        border: 7px solid transparent;
        border-bottom: 7px solid #fff;
        content: "";
        position: absolute;
        top: -14px;
        display: none; }
      .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner > div {
        margin-bottom: 25px; }
      .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .color-palette-label {
        font-size: 12px;
        font-weight: 500;
        margin: 0 0 10px 0;
        text-transform: uppercase; }
      .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start; }
        .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li {
          list-style: none;
          cursor: pointer;
          width: 40px;
          height: 40px;
          box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.05), inset -1px -1px 0 rgba(0, 0, 0, 0.05);
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          color: transparent;
          background-color: currentColor;
          display: inline-block; }
          .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li:hover {
            box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.75); }
          .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add {
            position: relative; }
            .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%; }
              .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer {
                padding: 0;
                width: 100%;
                height: 100%;
                border: none; }
                .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer:hover, .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer:active {
                  border-color: transparent; }
                .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer:after {
                  content: '+';
                  display: block;
                  top: 2px;
                  left: 2px;
                  width: 40px;
                  height: 40px;
                  text-indent: 0;
                  font-size: 28px;
                  line-height: 38px;
                  text-align: center;
                  font-weight: 100;
                  color: #111;
                  z-index: 2002;
                  position: absolute; }
                .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-preview, .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-alpha, .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-thumb-el {
                  margin-right: 0;
                  border: none;
                  width: 100%;
                  height: 100%; }
                .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-dd {
                  display: none; }
            .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add:after {
              font-family: 'nbd-vista' !important;
              position: absolute;
              content: "\E900";
              top: 0;
              left: 0;
              width: 40px;
              height: 40px;
              display: inline-block;
              line-height: 40px;
              text-align: center;
              color: #888888;
              font-size: 20px;
              speak: none;
              font-style: normal;
              font-weight: normal;
              font-variant: normal;
              text-transform: none; }
      .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner.show-popup .color-palette-popup {
        display: block; }
      .nbd-mode-vista .nbd-color-palette .nbd-color-palette-inner .color-palette-popup {
        min-width: 100px;
        min-height: 100px;
        position: absolute;
        display: none;
        top: 0;
        left: -50%; }
  .nbd-mode-vista .nb-ripple {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    overflow: hidden;
    -webkit-transform: translateZ(0);
    /* to contain zoomed ripple */
    transform: translateZ(0);
    border-radius: inherit;
    /* inherit from parent (rounded buttons etc) */
    pointer-events: none;
    /* allow user interaction */ }
  .nbd-mode-vista .nb-rippleWave {
    backface-visibility: hidden;
    position: absolute;
    border-radius: 50%;
    transform: scale(0.7);
    -webkit-transform: scale(0.7);
    background: white;
    opacity: 0.45;
    animation: ripple 1s forwards;
    -webkit-animation: ripple 1s forwards; }

@keyframes ripple-shadow {
  0% {
    box-shadow: 0 0 0 rgba(0, 0, 0, 0); }
  20% {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3); }
  100% {
    box-shadow: 0 0 0 rgba(0, 0, 0, 0); } }

@-webkit-keyframes ripple-shadow {
  0% {
    box-shadow: 0 0 0 rgba(0, 0, 0, 0); }
  20% {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3); }
  100% {
    box-shadow: 0 0 0 rgba(0, 0, 0, 0); } }

@keyframes ripple {
  to {
    transform: scale(24);
    opacity: 0; } }

@-webkit-keyframes ripple {
  to {
    -webkit-transform: scale(24);
    opacity: 0; } }
  .nbd-mode-vista .v-workspace {
    height: 500px;
    display: flex; }
  .nbd-mode-vista .v-sidebar {
    height: 500px;
    width: 260px; }
    .nbd-mode-vista .v-sidebar #tab-photo .tab-scroll {
      height: 100%; }
    .nbd-mode-vista .v-sidebar .v-tab-contents {
      height: calc(100% - 60px);
      background: #f4f4f4; }
    .nbd-mode-vista .v-sidebar .main-sidebar, .nbd-mode-vista .v-sidebar .v-tab-content {
      height: 100%; }
      .nbd-mode-vista .v-sidebar .main-sidebar.nbd-left, .nbd-mode-vista .v-sidebar .v-tab-content.nbd-left {
        transform: translateX(-33%);
        z-index: -1; }
      .nbd-mode-vista .v-sidebar .main-sidebar.nbd-right, .nbd-mode-vista .v-sidebar .v-tab-content.nbd-right {
        transform: translateX(33%);
        z-index: -1; }
    .nbd-mode-vista .v-sidebar .v-title {
      font-size: 14px;
      margin-bottom: 10px;
      display: block;
      padding: 10px 10px 0; }
    .nbd-mode-vista .v-sidebar .v-action {
      padding: 0 10px;
      margin-bottom: 20px; }
    .nbd-mode-vista .v-sidebar .v-content {
      position: relative;
      height: 100%; }
      .nbd-mode-vista .v-sidebar .v-content .text-editor .text-field {
        display: block;
        width: 100%;
        padding: 8px 10px;
        border-radius: 2px;
        outline: none;
        margin-bottom: 10px;
        background-color: #ccc; }
        .nbd-mode-vista .v-sidebar .v-content .text-editor .text-field:focus {
          border-color: #04b591; }
      .nbd-mode-vista .v-sidebar .v-content .text-editor input::-moz-placeholder, .nbd-mode-vista .v-sidebar .v-content .text-editor input:-moz-placeholder, .nbd-mode-vista .v-sidebar .v-content .text-editor input::-webkit-input-placeholder, .nbd-mode-vista .v-sidebar .v-content .text-editor input:-ms-input-placeholder {
        font-style: italic;
        color: #c8cbcc; }
      .nbd-mode-vista .v-sidebar .v-content .text-editor .editable-field {
        background-color: #fff; }
      .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items {
        border-bottom: 1px solid transparent;
        position: relative; }
        .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .pointer {
          position: absolute;
          z-index: 2;
          display: none;
          width: 10px;
          bottom: -5px;
          height: 10px;
          margin: 14px 0 0 -8px;
          border-top: 1px solid #ccc;
          border-right: 1px solid #ccc;
          transform: rotate(-45deg);
          transition: border-color .3s linear;
          background-color: #f4f4f4; }
        .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items.active-expanded {
          border-color: #ccc; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items.active-expanded .pointer {
            display: block; }
        .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .items {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          flex-wrap: wrap; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .items .nbd-icon-vista {
            font-size: 40px;
            line-height: 1.5; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .items .item {
            width: 75px;
            margin-bottom: 15px;
            cursor: pointer; }
            @media screen and (max-width: 320px) {
              .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .items .item {
                width: 65px; } }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .items .item .item-icon {
              padding: 5px;
              border-radius: 2px;
              background: #fff;
              border: 1px solid #ebebeb;
              text-align: center; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .items .item .item-info {
              font-size: 14px;
              margin-top: 5px;
              text-align: center; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .main-items .items .item .item-name {
              font-size: 12px; }
      .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded {
        overflow: hidden;
        margin-top: 20px; }
        .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items {
          opacity: 0;
          visibility: hidden;
          transform: translateY(50%);
          transition: all .6s; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items .content-item {
            display: none; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"] {
            transition: all .3s; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"]:hover {
              border-color: #ccc; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"] .form-upload {
              margin: 20px 10px 20px 0;
              border: 2px dashed #ebebeb;
              padding: 10px;
              pointer-events: auto;
              opacity: 0.5;
              display: flex;
              justify-content: center;
              align-items: center; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"] .form-upload i {
                pointer-events: auto;
                cursor: none;
                font-size: 40px;
                margin-right: 15px; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"].accept .form-upload {
              cursor: pointer;
              opacity: 1; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"].accept .form-upload i {
                cursor: pointer; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"] .allow-size {
              margin-bottom: 20px; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"] .allow-size span {
                display: block;
                text-align: left;
                line-height: 1.5; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"] .nbd-term {
              display: flex;
              justify-content: flex-start;
              align-items: center; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-upload"] .nbd-term .term-read {
                font-size: 14px;
                text-decoration: underline;
                font-weight: bold;
                cursor: pointer; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-url"] .form-group label {
            display: block;
            text-align: left;
            margin-bottom: 10px; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-url"] .form-group .input-group {
            display: flex; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-url"] .form-group .input-group input {
              padding: 3px 5px;
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
              border-right: none;
              border: 1px solid #04b591;
              width: 100%;
              box-shadow: none; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="image-url"] .form-group .input-group button {
              border: none;
              background-color: #04b591;
              color: #fff;
              margin: 0;
              border-radius: 0 3px 3px 0;
              padding: 0 10px;
              box-shadow: none;
              text-transform: capitalize; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="instagram"] {
            text-align: center; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="instagram"] .v-btn {
              display: flex;
              justify-content: center;
              align-items: center;
              margin: auto; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="instagram"] .v-btn i {
                font-size: 16px;
                color: #fff;
                margin-right: 10px; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .heading-title {
            display: inline-block;
            margin-bottom: 10px; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush {
            margin-bottom: 15px; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush button {
              display: flex;
              justify-content: space-between;
              align-items: center;
              outline: none;
              overflow: visible;
              margin: 0; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush button:focus {
                outline: none; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush button i {
                color: #fff;
                font-size: 18px; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-ranges {
              margin-bottom: 5px; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-ranges .range label {
                font-size: 14px; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .slide-input {
              margin: 8px 0 0; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .range-track {
              top: 11px; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-dropdown-menu {
              border: 1px solid #ebebeb;
              top: calc(100% + 5px);
              background-color: #fff; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-dropdown-menu ul {
                margin: 5px 0;
                list-style-type: none;
                max-height: 220px;
                border-radius: 2px;
                background-color: #fff;
                min-width: 95px; }
                .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-dropdown-menu ul li {
                  padding: 3px 10px;
                  text-align: left;
                  line-height: normal;
                  cursor: pointer; }
                  .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-dropdown-menu ul li:hover, .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-dropdown-menu ul li.active {
                    background-color: #f6f6f6; }
                  .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .brush .v-dropdown-menu ul li span {
                    font-size: 12px;
                    text-transform: capitalize; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette {
            min-width: 227px;
            position: absolute;
            right: 0;
            top: calc(100%);
            background-color: #fff;
            display: none;
            border: 1px solid #ebebeb; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette.show {
              display: block; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner {
              padding: 10px;
              position: relative; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner:before {
                border: 7px solid transparent;
                border-bottom: 7px solid #ebebeb;
                content: "";
                position: absolute;
                top: -15px;
                display: none; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner:after {
                border: 7px solid transparent;
                border-bottom: 7px solid #fff;
                content: "";
                position: absolute;
                top: -14px;
                display: none; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner > div {
                margin-bottom: 25px; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .color-palette-label {
                font-size: 12px;
                font-weight: 500;
                margin: 0 0 10px 0;
                text-transform: uppercase; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start; }
                .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li {
                  list-style: none;
                  cursor: pointer;
                  width: 40px;
                  height: 40px;
                  box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.05), inset -1px -1px 0 rgba(0, 0, 0, 0.05);
                  -webkit-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;
                  color: transparent;
                  background-color: currentColor;
                  display: inline-block; }
                  .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li:hover {
                    box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.75); }
                  .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add {
                    position: relative; }
                    .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span {
                      position: absolute;
                      top: 0;
                      left: 0;
                      width: 100%;
                      height: 100%; }
                      .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer {
                        padding: 0;
                        width: 100%;
                        height: 100%;
                        border: none; }
                        .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer:hover, .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer:active {
                          border-color: transparent; }
                        .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer:after {
                          content: '+';
                          display: block;
                          top: 2px;
                          left: 2px;
                          width: 40px;
                          height: 40px;
                          text-indent: 0;
                          font-size: 28px;
                          line-height: 38px;
                          text-align: center;
                          font-weight: 100;
                          color: #111;
                          z-index: 2002;
                          position: absolute; }
                        .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-preview, .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-alpha, .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-thumb-el {
                          margin-right: 0;
                          border: none;
                          width: 100%;
                          height: 100%; }
                        .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add span .sp-replacer .sp-dd {
                          display: none; }
                    .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .main-color-palette li.color-palette-add:after {
                      font-family: 'nbd-vista' !important;
                      position: absolute;
                      content: "\E900";
                      top: 0;
                      left: 0;
                      width: 40px;
                      height: 40px;
                      display: inline-block;
                      line-height: 40px;
                      text-align: center;
                      color: #888888;
                      font-size: 20px;
                      speak: none;
                      font-style: normal;
                      font-weight: normal;
                      font-variant: normal;
                      text-transform: none; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner.show-popup .color-palette-popup {
                display: block; }
              .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner .color-palette-popup {
                min-width: 100px;
                min-height: 100px;
                position: absolute;
                display: none;
                top: 0;
                left: -50%; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette {
            position: static;
            border: none;
            padding: 0;
            background-color: transparent; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-color-palette .nbd-color-palette-inner {
              padding: 0; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="draw"] .nbd-text-color-picker {
            top: 25px; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="qr-code"] .main-type {
            text-align: center; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="qr-code"] .main-input input {
            width: 100%;
            margin-bottom: 30px; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="qr-code"] button {
            text-transform: uppercase;
            margin-bottom: 20px; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="dropbox"] .v-btn-dropbox {
            display: inline-flex;
            justify-content: center;
            align-items: center; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .content-items div[data-type="dropbox"] .v-btn-dropbox i {
              color: #fff;
              font-size: 18px;
              margin-right: 10px; }
        .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded.loaded .content-items {
          opacity: 1;
          visibility: visible;
          transform: translateY(0); }
        .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .nbdesigner-gallery .nbdesigner-item {
          width: 33.33%;
          padding: 2px;
          opacity: 0;
          z-index: 3;
          display: inline-block;
          cursor: pointer;
          visibility: hidden; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .nbdesigner-gallery .nbdesigner-item.in-view {
            opacity: 1;
            visibility: visible; }
            .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .nbdesigner-gallery .nbdesigner-item.in-view .photo-desc {
              display: block; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .nbdesigner-gallery .nbdesigner-item .photo-desc {
            position: absolute;
            opacity: 0;
            visibility: hidden;
            display: none;
            -webkit-transform: translateY(50%);
            -ms-transform: translateY(50%);
            transform: translateY(50%);
            -webkit-transition: all .2s;
            transition: all .2s;
            bottom: 2px;
            left: 2px;
            padding: 2px 10px;
            display: block;
            width: calc(100% - 4px);
            text-align: left;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
            font-size: 12px; }
          .nbd-mode-vista .v-sidebar .v-content .v-elements .result-loaded .nbdesigner-gallery .nbdesigner-item:hover .photo-desc {
            opacity: 1;
            visibility: visible;
            -webkit-transform: translateY(0);
            -ms-transform: translateY(0);
            transform: translateY(0); }
      .nbd-mode-vista .v-sidebar .v-content .v-elements .loading-photo {
        position: relative;
        margin: auto; }
      .nbd-mode-vista .v-sidebar .v-content .info-support {
        position: absolute;
        top: 15px;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        z-index: 99;
        visibility: hidden;
        opacity: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        transform: translateY(-100%);
        transition: all .3s; }
        .nbd-mode-vista .v-sidebar .v-content .info-support.nbd-show {
          visibility: visible;
          opacity: 1;
          transform: translateY(0); }
        .nbd-mode-vista .v-sidebar .v-content .info-support span, .nbd-mode-vista .v-sidebar .v-content .info-support i {
          padding: 3px 10px;
          background: black;
          color: #fff; }
        .nbd-mode-vista .v-sidebar .v-content .info-support span {
          padding: 3px 20px;
          margin: auto;
          font-size: 18px;
          display: inline-block; }
        .nbd-mode-vista .v-sidebar .v-content .info-support i.close-result-loaded {
          display: inline-block;
          font-size: 25px;
          padding: 0 2px;
          cursor: pointer;
          margin-right: 9px; }
      .nbd-mode-vista .v-sidebar .v-content .mansory-wrap {
        margin-top: 15px; }
        .nbd-mode-vista .v-sidebar .v-content .mansory-wrap .mansory-item {
          visibility: visible;
          width: 33.33%;
          padding: 2px;
          opacity: 0;
          z-index: 3;
          cursor: pointer; }
          .nbd-mode-vista .v-sidebar .v-content .mansory-wrap .mansory-item.in-view {
            opacity: 1; }
          .nbd-mode-vista .v-sidebar .v-content .mansory-wrap .mansory-item .photo-desc {
            position: absolute;
            opacity: 0;
            visibility: hidden;
            -webkit-transform: translateY(50%);
            -ms-transform: translateY(50%);
            transform: translateY(50%);
            -webkit-transition: all .2s;
            transition: all .2s;
            bottom: 2px;
            left: 2px;
            padding: 2px 10px;
            display: block;
            width: -webkit-calc(100% - 4px);
            width: calc(100% - 4px);
            text-align: left;
            background: rgba(0, 0, 0, 0.3);
            color: #fff;
            font-size: 10px; }
          .nbd-mode-vista .v-sidebar .v-content .mansory-wrap .mansory-item:hover .photo-desc {
            opacity: 1;
            visibility: visible;
            -webkit-transform: translateY(0);
            -ms-transform: translateY(0);
            transform: translateY(0); }
    .nbd-mode-vista .v-sidebar #tab-text .v-btn {
      height: 40px;
      line-height: 40px; }
    .nbd-mode-vista .v-sidebar #tab-text .v-content {
      height: calc(100% - 102px); }
    .nbd-mode-vista .v-sidebar #tab-photo .v-content, .nbd-mode-vista .v-sidebar #tab-element .v-content {
      height: calc(100% - 92px); }
    .nbd-mode-vista .v-sidebar #tab-design .v-title {
      padding: 0; }
    .nbd-mode-vista .v-sidebar #tab-design .v-content {
      height: calc(100% - 55px); }
      .nbd-mode-vista .v-sidebar #tab-design .v-content .design-color {
        height: 100%; }
        .nbd-mode-vista .v-sidebar #tab-design .v-content .design-color .tab-scroll {
          height: calc(100% - 55px); }
      .nbd-mode-vista .v-sidebar #tab-design .v-content .tab-scroll.layout {
        margin-bottom: 20px; }
        .nbd-mode-vista .v-sidebar #tab-design .v-content .tab-scroll.layout .main-scrollbar {
          margin-bottom: 0; }
      .nbd-mode-vista .v-sidebar #tab-design .v-content .tab-scroll.bg-color .main-color .nbd-color-palette {
        position: static;
        border: none;
        padding: 0;
        background-color: transparent; }
    .nbd-mode-vista .v-sidebar #tab-design .items {
      display: flex;
      flex-wrap: wrap; }
      .nbd-mode-vista .v-sidebar #tab-design .items .item {
        padding: 5px;
        flex: 1 1 50%; }
        .nbd-mode-vista .v-sidebar #tab-design .items .item img {
          margin: auto;
          object-fit: contain; }
    .nbd-mode-vista .v-sidebar .v-image-toolbar .v-content {
      height: calc(100% - 75px); }
    .nbd-mode-vista .v-sidebar .nbd-nav-tabs {
      display: flex;
      border-bottom: 1px solid #04b591;
      min-height: 35px;
      padding: 10px 10px 0;
      margin-bottom: 10px; }
      .nbd-mode-vista .v-sidebar .nbd-nav-tabs .nbd-nav-tab {
        margin-right: 5px;
        cursor: pointer;
        padding: 5px 10px;
        margin-bottom: -1px;
        border: 1px solid transparent; }
        .nbd-mode-vista .v-sidebar .nbd-nav-tabs .nbd-nav-tab.active {
          border: 1px solid #04b591;
          border-bottom: 1px solid #f4f4f4;
          border-top-left-radius: 4px;
          border-top-right-radius: 4px; }
      .nbd-mode-vista .v-sidebar .nbd-nav-tabs .v-title {
        margin-bottom: 0; }
    .nbd-mode-vista .v-sidebar .nbd-search {
      position: relative;
      margin-bottom: 15px;
      width: 100%; }
      .nbd-mode-vista .v-sidebar .nbd-search input {
        min-height: 35px;
        padding: 5px 35px 5px 10px;
        width: 100%;
        -webkit-border-radius: 2px;
        border-radius: 2px;
        outline: 0;
        border: 1px solid #ccc;
        background-color: #fff; }
      .nbd-mode-vista .v-sidebar .nbd-search i {
        position: absolute;
        font-size: 24px;
        right: 10px;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        color: #ccc; }
    .nbd-mode-vista .v-sidebar #nbdesigner_dropbox a {
      padding: 5px; }
  .nbd-mode-vista .v-layout {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    width: calc(100% - 290px);
    margin-left: 30px;
    height: calc(100% - 60px);
    position: relative; }
  .nbd-mode-vista .v-toolbox .header-box {
    padding: 10px; }
  .nbd-mode-vista .v-toolbox .has-box-more {
    display: flex;
    margin-bottom: 10px; }
    .nbd-mode-vista .v-toolbox .has-box-more .link-breadcrumb {
      margin-left: auto;
      display: flex;
      justify-content: center;
      align-items: center; }
      .nbd-mode-vista .v-toolbox .has-box-more .link-breadcrumb .link-item {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 3px;
        margin-left: 5px;
        border: 1px solid #ccc;
        border-radius: 2px;
        width: 28px;
        height: 28px; }
        .nbd-mode-vista .v-toolbox .has-box-more .link-breadcrumb .link-item i {
          font-size: 18px;
          color: #ccc; }
        .nbd-mode-vista .v-toolbox .has-box-more .link-breadcrumb .link-item.active {
          background: #ccc; }
          .nbd-mode-vista .v-toolbox .has-box-more .link-breadcrumb .link-item.active i {
            color: #fff; }
  .nbd-mode-vista .v-toolbox .main-box, .nbd-mode-vista .v-toolbox .main-box-more {
    padding: 0 10px 10px; }
    .nbd-mode-vista .v-toolbox .main-box .toolbox-row, .nbd-mode-vista .v-toolbox .main-box-more .toolbox-row {
      margin-bottom: 10px; }
  .nbd-mode-vista .v-toolbox .footer-box {
    border-top: 1px solid #ebebeb; }
  .nbd-mode-vista .v-toolbox .v-assets {
    display: flex;
    justify-content: space-between;
    align-items: center; }
    .nbd-mode-vista .v-toolbox .v-assets .v-asset {
      width: 40px;
      height: 40px;
      line-height: 40px;
      border: 1px solid #04b591;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 2px;
      margin: 0;
      padding: 0; }
      .nbd-mode-vista .v-toolbox .v-assets .v-asset i {
        color: #04b591;
        font-size: 18px; }
      .nbd-mode-vista .v-toolbox .v-assets .v-asset:hover, .nbd-mode-vista .v-toolbox .v-assets .v-asset:active, .nbd-mode-vista .v-toolbox .v-assets .v-asset:focus {
        background-color: #f6f6f6; }
      .nbd-mode-vista .v-toolbox .v-assets .v-asset.active {
        background-color: #f6f6f6; }
        .nbd-mode-vista .v-toolbox .v-assets .v-asset.active i {
          font-weight: bold; }
    .nbd-mode-vista .v-toolbox .v-assets .v-asset-margin {
      margin-right: 5px; }
      .nbd-mode-vista .v-toolbox .v-assets .v-asset-margin:last-child {
        margin-right: 0; }
    .nbd-mode-vista .v-toolbox .v-assets .v-asset-title {
      font-size: 12px;
      display: inline-block;
      color: #bbb; }
  .nbd-mode-vista .v-toolbox .v-triangle {
    position: relative; }
    .nbd-mode-vista .v-toolbox .v-triangle:after, .nbd-mode-vista .v-toolbox .v-triangle:before {
      left: 50%;
      transform: translateX(-50%); }
    .nbd-mode-vista .v-toolbox .v-triangle:before {
      border: 7px solid transparent;
      border-bottom: 7px solid #ebebeb;
      content: "";
      position: absolute;
      top: -15px; }
    .nbd-mode-vista .v-toolbox .v-triangle:after {
      border: 7px solid transparent;
      border-bottom: 7px solid #fff;
      content: "";
      position: absolute;
      top: -14px; }
    .nbd-mode-vista .v-toolbox .v-triangle[data-pos="left"]:after, .nbd-mode-vista .v-toolbox .v-triangle[data-pos="left"]:before {
      left: 65%; }
    .nbd-mode-vista .v-toolbox .v-triangle[data-pos="center"]:after, .nbd-mode-vista .v-toolbox .v-triangle[data-pos="center"]:before {
      left: 50%; }
  .nbd-mode-vista .v-toolbox .v-triangle-box {
    position: absolute;
    border: 7px solid transparent;
    border-bottom: 7px solid #ebebeb;
    content: "";
    top: -14px;
    transform: translateX(-50%);
    transition: all .3s; }
    .nbd-mode-vista .v-toolbox .v-triangle-box:after {
      position: absolute;
      content: '';
      height: 0;
      width: 0;
      border: 7px solid transparent;
      border-bottom: 7px solid white;
      top: -6px;
      left: -7px; }
  .nbd-mode-vista .v-toolbox .toolbox-lock .items .item.active i {
    color: red; }
  .nbd-mode-vista .v-toolbox .v-toolbox-item {
    position: absolute;
    border: 1px solid #ebebeb;
    min-width: 250px;
    border-radius: 2px;
    background: #fff;
    transition: visibility .2s, opacity .2s;
    visibility: hidden;
    opacity: 0;
    z-index: 9999;
    transform: translateX(-50%); }
    .nbd-mode-vista .v-toolbox .v-toolbox-item .link-back {
      display: none; }
    .nbd-mode-vista .v-toolbox .v-toolbox-item.show-box-more .main-box {
      display: none; }
    .nbd-mode-vista .v-toolbox .v-toolbox-item.show-box-more .main-box-more {
      display: block; }
    .nbd-mode-vista .v-toolbox .v-toolbox-item.show-box-more .link-back {
      display: inline-block; }
    .nbd-mode-vista .v-toolbox .v-toolbox-item.show-box-more .link-more {
      display: none; }
    .nbd-mode-vista .v-toolbox .v-toolbox-item.nbd-show {
      visibility: visible;
      opacity: 1; }
  .nbd-mode-vista .v-toolbox .v-btn.btn-color {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    box-shadow: none;
    padding: 5px;
    border: 1px solid #ebebeb; }
    .nbd-mode-vista .v-toolbox .v-btn.btn-color .color-selected {
      width: 85px;
      height: 24px;
      background-color: red;
      line-height: 1;
      border: 1px solid #e6e6e6; }
    .nbd-mode-vista .v-toolbox .v-btn.btn-color i {
      color: #04b591;
      font-size: 16px;
      line-height: normal;
      margin-left: 10px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbar-menu-header {
    display: flex; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbar-menu-header .toolbar-header-line {
      background-color: rgba(63, 70, 82, 0.15);
      -ms-flex-positive: 1;
      flex-grow: 1;
      height: 1px;
      margin: auto; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbar-menu-header .toolbar-separator {
      color: rgba(63, 70, 82, 0.4);
      font-size: 12px;
      font-weight: 500;
      margin: 8px 15px;
      min-width: 40px;
      text-align: center;
      text-transform: uppercase; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbar-font-search {
    position: relative;
    max-height: 220px;
    overflow: hidden;
    padding: 10px 15px; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbar-font-search input {
      background-color: #fff;
      width: 100%;
      -webkit-border-radius: 2px;
      border-radius: 2px;
      outline: 0;
      padding: 5px 20px 5px 5px;
      border: 1px solid #ebebeb; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbar-font-search i {
      position: absolute;
      right: 15px;
      top: 10px;
      width: 24px;
      height: 33px;
      line-height: 33px;
      cursor: pointer; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .tab-scroll {
    max-height: 220px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .v-dropdown .v-dropdown-menu .items .item {
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .v-dropdown .v-dropdown-menu .items .item .font-right {
      margin-left: auto; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .v-dropdown .v-dropdown-menu .items .item i.checked {
      font-size: 14px;
      display: none;
      color: #04b591;
      font-weight: bold;
      margin-left: 10px; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .v-dropdown .v-dropdown-menu .items .item span {
      color: #04b591; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .v-dropdown .v-dropdown-menu .items .item.active i.checked {
      display: block; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .v-dropdown .v-dropdown-menu .items .item.active, .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-family .v-dropdown .v-dropdown-menu .items .item:hover {
      background-color: #f6f6f6; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-last {
    display: flex;
    justify-content: space-between;
    align-items: center; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .items {
    margin: 10px 0; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .items .item {
      padding: 0 5px;
      display: flex;
      justify-content: space-between;
      align-items: center; }
      .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .items .item:hover, .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .items .item.active {
        background-color: #f6f6f6; }
      .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .items .item i.checked {
        font-size: 14px;
        display: none;
        color: #04b591;
        font-weight: bold; }
      .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .items .item span {
        color: #04b591; }
      .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .items .item.active i.checked {
        display: block; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-font-size .tab-scroll {
    max-height: 220px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .toolbox-color-palette .tab-scroll {
    max-height: 100px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .v-btn.btn-font-family {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 28px;
    font-size: 14px;
    text-transform: capitalize;
    background: #fff;
    color: #04b591;
    box-shadow: none;
    border: 1px solid #ebebeb;
    padding: 5px;
    height: auto; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .v-btn.btn-font-family span {
      font-weight: bold; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .v-btn.btn-font-family i {
      font-size: 16px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .v-btn.btn-font-size {
    background: #fff;
    box-shadow: none;
    border: 1px solid #ebebeb;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: normal; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .v-btn.btn-font-size input {
      padding: 0;
      margin: 0;
      max-width: 40px;
      box-shadow: none;
      background-color: transparent;
      color: #04b591;
      border: none; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .v-btn.btn-font-size i {
      margin-left: 30px;
      font-size: 12px;
      color: #04b591; }
      .nbd-mode-vista .v-toolbox .v-toolbox-text .v-btn.btn-font-size i:before {
        color: #04b591; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .v-dropdown .v-dropdown-menu {
    width: 100%;
    background: #fff;
    border: 1px solid #ebebeb;
    border-top: none; }
  .nbd-mode-vista .v-toolbox .v-toolbox-text .footer-box .items {
    display: flex;
    justify-content: space-around;
    align-items: center; }
    .nbd-mode-vista .v-toolbox .v-toolbox-text .footer-box .items .item {
      flex: 1 1 50%;
      text-align: center;
      padding: 5px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center; }
      .nbd-mode-vista .v-toolbox .v-toolbox-text .footer-box .items .item i {
        color: #04b591; }
      .nbd-mode-vista .v-toolbox .v-toolbox-text .footer-box .items .item.item-reset {
        border-right: 1px solid #ebebeb; }
  .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first {
    display: flex;
    justify-content: space-between;
    align-items: center; }
    .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-general .items .item {
      margin-right: 10px;
      text-align: center; }
      .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-general .items .item:last-child {
        margin-right: 0; }
      .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-general .items .item.active .v-asset {
        background-color: #f6f6f6; }
    .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-directional {
      position: relative;
      margin: -28px 0 0 20px;
      display: inline-block;
      width: 68px;
      height: 68px; }
      .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-directional .item-direct {
        cursor: pointer;
        position: absolute;
        width: 26px;
        height: 22px;
        text-align: center;
        border: 1px solid #ebebeb;
        border-top: none; }
        .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-directional .item-direct i {
          font-size: 18px;
          line-height: normal; }
        .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-directional .item-direct:first-child {
          top: 0;
          left: 21px; }
        .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-directional .item-direct:nth-child(2) {
          top: 23px;
          right: -3px; }
        .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-directional .item-direct:nth-child(3) {
          bottom: 0;
          left: 21px; }
        .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-first .toolbox-directional .item-direct:nth-child(4) {
          top: 23px;
          left: -3px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-assets .v-asset i {
    font-size: 28px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-ranges {
    width: calc(100% - 100px);
    height: 40px;
    display: flex;
    align-items: center; }
    .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-ranges .main-track {
      position: relative;
      width: 100%; }
      .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-ranges .main-track .slide-input {
        background-color: transparent;
        border: none;
        height: 7px;
        outline: none;
        padding: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 100;
        margin-top: -3px;
        -webkit-appearance: none; }
        .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-ranges .main-track .slide-input::-webkit-slider-thumb {
          appearance: none;
          width: 7px;
          height: 20px;
          background: #04b591;
          cursor: pointer; }
        .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-ranges .main-track .slide-input::-moz-range-thumb {
          width: 7px;
          height: 20px;
          background: #04b591;
          cursor: pointer; }
      .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-ranges .main-track .range-track {
        background-color: #aaa;
        height: 2px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%; }
      .nbd-mode-vista .v-toolbox .v-toolbox-image .toolbox-last .v-ranges .main-track .snap-guide {
        position: absolute;
        border-left: 2px solid #aaa;
        left: 50%;
        top: 0;
        height: 12px;
        margin-top: 2px;
        z-index: 90; }
  .nbd-mode-vista .v-toolbox .v-toolbox-image .footer-box .main-footer {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-group .header-box {
    margin-bottom: 10px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-group .main-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
    cursor: pointer; }
  .nbd-mode-vista .v-toolbox .v-toolbox-path .toolbox-color-palette {
    position: relative; }
  .nbd-mode-vista .v-toolbox .v-toolbox-path .nbd-color-palette {
    right: auto;
    left: 0;
    position: absolute;
    top: 47px; }
    .nbd-mode-vista .v-toolbox .v-toolbox-path .nbd-color-palette .close-block {
      display: flex;
      margin-bottom: 0;
      position: absolute;
      top: 10px;
      right: 10px; }
  .nbd-mode-vista .v-toolbox .v-toolbox-path .footer-box .main-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
    cursor: pointer; }
  .nbd-mode-vista .nbd-stages {
    width: 100%;
    height: 100%; }
    .nbd-mode-vista .nbd-stages .stages-inner {
      height: 100%;
      width: 100%;
      position: relative;
      overflow: hidden; }
      .nbd-mode-vista .nbd-stages .stages-inner .stage {
        overflow: hidden;
        background-color: #fafafa;
        height: 100%;
        width: 100%;
        display: block;
        text-align: center;
        padding: 40px 50px 40px 40px;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1; }
        .nbd-mode-vista .nbd-stages .stages-inner .stage.nbd-active {
          z-index: 9; }
        .nbd-mode-vista .nbd-stages .stages-inner .stage.hidden {
          opacity: 0;
          z-index: 0;
          -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0); }
        .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main {
          position: relative;
          background-color: #fff;
          margin: auto 0; }
          .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .stage-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ebebeb; }
          .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap {
            position: absolute; }
            .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .design-zone, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-grid, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .bounding-layers, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-snapLines, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-overlay, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-guideline {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%; }
            .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-grid, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .bounding-layers, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-snapLines, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-overlay, .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-guideline {
              pointer-events: none; }
            .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .bounding-layers {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%; }
              .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .bounding-layers .bounding-layers-inner {
                position: relative;
                width: 100%;
                height: 100%; }
                .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .bounding-layers .bounding-layers-inner .bounding-rect {
                  position: absolute;
                  display: inline-block;
                  visibility: hidden;
                  top: -20px;
                  left: -20px;
                  width: 10px;
                  height: 10px;
                  border: 1px dashed #ddd;
                  transform-origin: 0% 0%; }
                .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .bounding-layers .bounding-layers-inner .layer-coordinates {
                  position: absolute;
                  display: inline-block;
                  font-size: 9px;
                  font-family: monospace;
                  color: #404762;
                  visibility: hidden;
                  transform: translate(calc(-100% - 10px), calc(-100% + 5px));
                  text-shadow: 1px 1px #fff; }
                .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .bounding-layers .bounding-layers-inner .layer-angle {
                  position: absolute;
                  display: inline-block;
                  font-family: monospace;
                  color: #404762;
                  visibility: hidden;
                  text-shadow: 1px 1px #fff; }
            .nbd-mode-vista .nbd-stages .stages-inner .stage .stage-main .design-wrap .stage-snapLines .stage-snapLines-inner {
              position: relative;
              width: 100%;
              height: 100%; }
    .nbd-mode-vista .nbd-stages .page-toolbar {
      width: 40px;
      position: absolute;
      top: 50%;
      right: -40px;
      transform: translateY(-50%);
      height: unset;
      margin: 0; }
      @media screen and (max-width: 475px) {
        .nbd-mode-vista .nbd-stages .page-toolbar {
          bottom: 40px; } }
      @media screen and (max-width: 420px) {
        .nbd-mode-vista .nbd-stages .page-toolbar {
          bottom: 75px; } }
      @media screen and (max-width: 375px) {
        .nbd-mode-vista .nbd-stages .page-toolbar {
          bottom: 110px; } }
      .nbd-mode-vista .nbd-stages .page-toolbar .page-main {
        height: 100%;
        text-align: center; }
        .nbd-mode-vista .nbd-stages .page-toolbar .page-main ul {
          list-style-type: none;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center; }
          .nbd-mode-vista .nbd-stages .page-toolbar .page-main ul li {
            padding: 10px 0;
            cursor: pointer; }
            @media screen and (max-width: 767px) {
              .nbd-mode-vista .nbd-stages .page-toolbar .page-main ul li {
                padding: 5px 10px; } }
            .nbd-mode-vista .nbd-stages .page-toolbar .page-main ul li:hover i {
              opacity: 1; }
            .nbd-mode-vista .nbd-stages .page-toolbar .page-main ul li.disabled:hover i {
              opacity: 0.5; }
            .nbd-mode-vista .nbd-stages .page-toolbar .page-main ul li i {
              font-size: 24px;
              opacity: 0.5; }
            .nbd-mode-vista .nbd-stages .page-toolbar .page-main ul li span {
              font-size: 18px; }
    .nbd-mode-vista .nbd-stages .snapline {
      position: absolute; }
    .nbd-mode-vista .nbd-stages .h-snapline {
      top: -9999px;
      left: -20px;
      width: calc(100% + 40px);
      height: 3px !important;
      background-image: linear-gradient(to right, rgba(214, 96, 96, 0.95) 1px, transparent 1px);
      background-size: 2px 1px;
      background-repeat: repeat-x; }
    .nbd-mode-vista .nbd-stages .v-snapline {
      left: -9999px;
      top: -20px;
      height: calc(100% + 40px);
      width: 3px !important;
      background-image: linear-gradient(to bottom, rgba(214, 96, 96, 0.95) 1px, transparent 1px);
      background-size: 1px 2px;
      background-repeat: repeat-y; }
  .nbd-mode-vista .nbd-toolbar-zoom {
    position: absolute;
    bottom: 10px;
    right: 30px;
    z-index: 99; }
    .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer {
      border-radius: 60px;
      background-color: #fff;
      box-shadow: 1px 0 10px rgba(0, 0, 0, 0.08);
      border: none;
      display: flex;
      justify-content: center;
      align-items: center; }
      .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item {
        padding: 5px;
        display: flex;
        align-items: center;
        cursor: pointer; }
        .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item i {
          font-size: 24px; }
        .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-fullscreen {
          border-right: 1px solid #ebebeb; }
        .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level {
          position: relative; }
          .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level span {
            font-size: 14px;
            font-weight: 500; }
          .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover {
            min-width: auto;
            top: -15px;
            left: 50%;
            transform: translate(-50%, -100%);
            box-shadow: 1px 0 10px rgba(0, 0, 0, 0.08);
            background-color: #fff; }
            .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover:after, .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover:before {
              transform: translateX(-50%) rotate(-180deg);
              bottom: -14px;
              top: auto; }
            .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-list {
              margin: 10px 0; }
            .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item {
              font-size: 13px;
              padding: 5px 40px 5px 15px;
              cursor: pointer; }
              .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item.active {
                position: relative; }
                .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item.active:before {
                  font-family: nbd-vista !important;
                  content: "\E911";
                  position: absolute;
                  top: 4px;
                  right: 10px;
                  speak: none;
                  font-style: normal;
                  font-weight: 400;
                  font-variant: normal;
                  text-transform: none;
                  line-height: 1;
                  -webkit-font-smoothing: antialiased;
                  font-size: 18px;
                  color: #04b591; }
              .nbd-mode-vista .nbd-toolbar-zoom .zoomer-toolbar .nbd-main-zoomer .zoomer-item.zoomer-level .zoomer-popover .zoomer-popover-item:hover {
                background-color: #f6f6f6; }

.fullScreenMode .design-zone {
  pointer-events: none; }

.fullScreenMode .page-toolbar {
  display: none; }

.fullScreenMode .stage {
  padding: 0; }

.fullScreenMode .nbd-stages {
  width: 100vw;
  background: #000; }
  .fullScreenMode .nbd-stages .ps__scrollbar-x-rail, .fullScreenMode .nbd-stages .ps__scrollbar-y-rail {
    display: none; }

.fullScreenMode .fullscreen-stage-nav {
  display: inline-block; }

.fullScreenMode .nbd-mode-vista .nbd-stages .stages-inner .stage {
  background-color: #000; }

.fullscreen-stage-nav {
  position: absolute;
  bottom: 40px;
  right: 40px;
  display: none; }

.nbd-mode-vista.nbd-mobile .v-toolbar {
  margin-bottom: 0;
  position: relative;
  z-index: 9; }

.nbd-mode-vista.nbd-mobile .v-toolbar .main-toolbar {
  border-color: transparent;
  flex-wrap: wrap; }

.nbd-mode-vista.nbd-mobile .v-toolbar .main-toolbar .v-toolbar-item {
  margin: auto 0;
  overflow-x: auto; }

.nbd-mode-vista.nbd-mobile .v-toolbar .main-toolbar .v-toolbar-item.right-toolbar .v-main-menu {
  justify-content: center; }

.nbd-mode-vista.nbd-mobile .v-tab-contents .v-tab-content {
  margin-top: 15px; }

.nbd-mode-vista.nbd-mobile .nbd-context-menu {
  display: none; }

.nbd-mode-vista.nbd-mobile .nbd-warning {
  display: none; }

.nbd-mode-vista.nbd-mobile .nbd-tool-lock {
  display: none; }

.nbd-mode-vista.nbd-mobile .nbd-toasts {
  display: none; }

.nbd-mode-vista.nbd-mobile .nbd-toolbar-zoom {
  bottom: 75px;
  display: none; }

.nbd-mode-vista.nbd-mobile .v-ranges .range .main-track .slide-input::-webkit-slider-thumb {
  width: 20px;
  height: 20px; }

.nbd-mode-vista.nbd-mobile .v-sidebar {
  position: relative;
  width: 0;
  opacity: 0;
  visibility: hidden;
  z-index: -1; }

.nbd-mode-vista.nbd-mobile .v-sidebar.active {
  width: 100%;
  opacity: 1;
  visibility: visible;
  z-index: 1; }

.nbd-mode-vista.nbd-mobile .v-sidebar .v-content .v-elements .main-items .items .item {
  width: 100px; }

.nbd-mode-vista.nbd-mobile .v-layout {
  position: relative;
  width: 0;
  opacity: 0;
  visibility: hidden;
  z-index: -1; }

.nbd-mode-vista.nbd-mobile .v-layout.active {
  width: 100%;
  opacity: 1;
  visibility: visible;
  z-index: 1; }

.nbd-mode-vista.nbd-mobile .nbd-stages .page-toolbar {
  height: auto;
  width: auto;
  background-color: #fff;
  left: 50%;
  right: auto;
  transform: translateX(-50%);
  bottom: -50px;
  top: auto; }

.nbd-mode-vista.nbd-mobile .nbd-stages .page-toolbar .page-main {
  background-color: #fff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 2px 1px -1px rgba(0, 0, 0, 0.12); }

.nbd-mode-vista.nbd-mobile .nbd-stages .page-toolbar .page-main ul {
  flex-direction: row;
  align-items: center; }

.nbd-mode-vista.nbd-mobile .nbd-stages .page-toolbar .page-main ul li {
  display: flex;
  padding: 5px 10px; }

.nbd-mode-vista.nbd-mobile .nbd-stages .page-toolbar .page-main ul li {
  padding: 5px 10px; }

.nbd-mode-vista.nbd-mobile .nbd-toolbar-zoom {
  bottom: 75px;
  display: none; }

.nbd-mode-vista.nbd-mobile .v-layout {
  margin-left: 0; }

.nbd-mode-vista.nbd-mobile .v-sidebar .v-tab-contents {
  background: #fafafa; }

.nbd-mode-vista.nbd-mobile .v-sidebar .nbd-nav-tabs .nbd-nav-tab.active {
  border-bottom: 1px solid #fafafa; }

.nbd-mode-vista.nbd-mobile .v-sidebar .v-content .v-elements .main-items .pointer {
  background-color: #fafafa; }

.nbd-mode-vista.nbd-mobile .nbd-stages .stages-inner .stage {
  padding: 25px 10px 10px; }

.nbd-mode-vista.nbd-mobile .v-main-menu .v-menu-item:hover {
  background-color: transparent; }

.nbd-mode-vista.nbd-mobile .v-main-menu .v-menu-item.active {
  background-color: #04b591; }

.nbd-mode-vista.nbd-mobile .v-toolbar .left-toolbar {
  border-bottom: 1px solid #04b591; }

.nbd-mode-vista.nbd-mobile .v-toolbar .main-toolbar .right-toolbar ul li:first-child {
  border-right: 1px solid #04b591; }

.nbd-mode-vista .v-toolbar .v-menu-item.v-tab-layer {
  display: none; }

.nbd-mode-vista.nbd-mobile .v-toolbar .v-menu-item.v-tab-layer {
  display: flex; }

@media screen and (max-width: 475px) {
  .nbd-mode-vista.nbd-mobile .v-workspace {
    height: 440px; } }


/*# sourceMappingURL=vista.css.map*/