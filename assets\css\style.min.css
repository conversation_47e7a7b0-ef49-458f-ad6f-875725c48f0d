/* fontNBD  */
@font-face {
  font-family: 'FontNBD';
  src:  url('fonts/FontNBD.eot?1ac8ga');
  src:  url('fonts/FontNBD.eot?1ac8ga#iefix') format('embedded-opentype'),
    url('fonts/FontNBD.ttf?1ac8ga') format('truetype'),
    url('fonts/FontNBD.woff?1ac8ga') format('woff'),
    url('fonts/FontNBD.svg?1ac8ga#FontNBD') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^=nbd-icon-], [class*=" nbd-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'FontNBD' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.nbd-icon-sports-shoe:before {
  content: "\e90f";
}
.nbd-icon-file-png:before {
  content: "\e910";
}
.nbd-icon-file-pdf:before {
  content: "\e911";
}
.nbd-icon-file-jpg:before {
  content: "\e912";
}
.nbd-icon-file-eps:before {
  content: "\e914";
}
.nbd-icon-document-file-png:before {
  content: "\e915";
}
.nbd-icon-document-file-png2:before {
  content: "\e916";
}
.nbd-icon-document-file-pdf:before {
  content: "\e917";
}
.nbd-icon-document-file-pdf2:before {
  content: "\e918";
}
.nbd-icon-document-file-jpg:before {
  content: "\e919";
}
.nbd-icon-document-file-jpg2:before {
  content: "\e91a";
}
.nbd-icon-layers:before {
  content: "\e031";
}
.nbd-icon-scissors:before {
  content: "\e035";
}
.nbd-icon-chart:before {
  content: "\e909";
}
.nbd-icon-vote:before {
  content: "\e90a";
}
.nbd-icon-mouse:before {
  content: "\e913";
}
.nbd-icon-medal:before {
  content: "\e90b";
}
.nbd-icon-stack:before {
  content: "\e90c";
}
.nbd-icon-narrow:before {
  content: "\e90d";
}
.nbd-icon-bookmark:before {
  content: "\e900";
}
.nbd-icon-upload-to-cloud:before {
  content: "\e91b";
}
.nbd-icon-add-user:before {
  content: "\e901";
}
.nbd-icon-align-bottom:before {
  content: "\e902";
}
.nbd-icon-align-horizontal-middle:before {
  content: "\e903";
}
.nbd-icon-align-left:before {
  content: "\e904";
}
.nbd-icon-align-right:before {
  content: "\e905";
}
.nbd-icon-align-top:before {
  content: "\e906";
}
.nbd-icon-align-vertical-middle:before {
  content: "\e907";
}
.nbd-icon-ruler:before {
  content: "\e908";
}
.nbd-icon-chart2:before {
  content: "\e90e";
}
.nbd-icon-undo2:before {
  content: "\e967";
}
.nbd-icon-redo2:before {
  content: "\e968";
}
.nbd-icon-spinner2:before {
  content: "\e97b";
}
.nbd-icon-spinner10:before {
  content: "\e983";
}
/* end. fontNBD  */
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    height: 100%!important
}

body {
    background-color: #fff; 
/*    background-image: url(../images/pattern.jpg);*/
    background-color: #f1f1f1;
    margin: 0;
    font-family: 'Poppins', sans-serif;
    font-weight: 300
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
span,
p,
i,legend, textarea,
a {
    font-family: 'Poppins', sans-serif;
    font-weight: 300
}

ul {
    padding-left: 0
}

li {
    list-style: none
}

a {
    background-color: transparent
}

a,
a:hover,
a:active {
    text-decoration: none;
    cursor: pointer
}

a:hover {
    color: #cc324b
}

a:active,
a:hover {
    outline: 0
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

button {
    overflow: visible
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-widget-content {
    background: none;
    color: #394264;
    border: none;
    margin: 0
}

.ps-scrollbar-x,
.ps-scrollbar-x-rail {
    display: none !important
}
#nbd-viewport .ps-scrollbar-x,
#nbd-viewport .ps-scrollbar-x-rail {
    display: unset !important
}

.ps-container .ps-scrollbar-y {
    width: 6px!important;
    right: 0!important;
/*    max-height: 100px;*/
    background-color: #394264
}

.cmn-toggle {
    position: absolute;
    margin-left: -9999px;
    visibility: hidden
}

.cmn-toggle + label {
    display: block;
    position: relative;
    cursor: pointer;
    outline: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

input.cmn-toggle-round + label {
    padding: 2px;
    width: 40px;
    height: 20px;
    background-color: #ddd;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    border-radius: 10px
}

input.cmn-toggle-round + label:before,
input.cmn-toggle-round + label:after {
    display: block;
    position: absolute;
    top: 1px;
    left: 1px;
    bottom: 1px;
    content: ""
}

input.cmn-toggle-round + label:before {
    right: 1px;
    background-color: #f1f1f1;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    -ms-border-radius: 10px;
    -o-border-radius: 10px;
    border-radius: 10px;
    -webkit-transition: background .4s;
    -moz-transition: background .4s;
    -o-transition: background .4s;
    transition: background .4s
}

input.cmn-toggle-round + label:after {
    width: 18px;
    background-color: #fff;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    -ms-border-radius: 100%;
    -o-border-radius: 100%;
    border-radius: 100%;
    -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    -webkit-transition: margin .4s;
    -moz-transition: margin .4s;
    -o-transition: margin .4s;
    transition: margin .4s
}

input.cmn-toggle-round:checked + label:before {
    background-color: #cc324b
}

input.cmn-toggle-round:checked + label:after {
    margin-left: 20px
}

.tooltip > .tooltip-inner {
    background-color: #394264
}

.tooltip.top .tooltip-arrow {
    border-top-color: #394264
}

.tooltip.top-left .tooltip-arrow {
    border-top-color: #394264
}

.tooltip.top-right .tooltip-arrow {
    border-top-color: #394264
}

.tooltip.right .tooltip-arrow {
    border-right-color: #394264
}

.tooltip.left .tooltip-arrow {
    border-left-color: #394264
}

.tooltip.bottom .tooltip-arrow {
    border-bottom-color: #394264
}

.tooltip.bottom-left .tooltip-arrow {
    border-bottom-color: #394264
}

.tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #394264
}

button.confirm,
button.cancel {
    background: #394264;
    border: none;
    border-radius: 0;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12)
}

button.confirm:hover,
button.confirm:focus,
button.cancel:hover {
    outline: none;
    background: #394264;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)
}

button.cancel,
button.cancel:hover {
    background: #fff
}

.dg-slider.ui-slider {
    background: #f4f4f4 none repeat scroll 0 0;
    border: 1px solid #cacaca;
    border-radius: 2px;
    cursor: pointer;
    height: 3px;
    margin-top: 10px;
    margin-bottom: 5px
}

.dg-slider.ui-slider .ui-slider-range {
    background: #bebebe;
    border: 0 none;
    border-radius: 0
}

.dg-slider.ui-slider .ui-slider-handle {
    background: #fff none repeat scroll 0 0;
    border: 1px solid #cacaca;
    border-radius: 50%;
    cursor: pointer;
    height: 18px;
    outline: medium none;
    top: -8px;
    width: 18px
}

.container-dg-slider {
    padding-right: 15px
}

.modal-content {
    border-radius: 0;
    border: none
}

.modal-header {
    padding: 15px 15px 0
}

.modal-header button.close {
    width: 25px;
    height: 25px;
    background: #fff;
    opacity: 1;
    text-align: center;
    border-radius: 50%;
    color: #aaa;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    cursor: pointer;
    margin-top: -5px
}

.modal-header button.close:hover {
    color: #000;
    outline: none;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15)
}

.nbdesigner_modal_tab {
    margin-bottom: -1px;
    font-size: 0;
}

.nbdesigner_modal_tab li {
    padding: 7px 5px;
    background: #ddd;
    text-align: center;
    border: 1px solid #ddd;
    min-width: 100px;
    margin-top: 2px;
    margin: 2px 2px 0 0;
    font-size: 14px;    
}

.nbdesigner_modal_tab li.active {
    background: #fff;
    border-bottom: 1px solid #fff;
}

.nbdesigner_modal_tab li a {
    color: #394264;
    text-decoration: none;
    font-weight: 700
}

#dg-myclipart .nbdesigner_modal_tab li span,
#dg-expand-feature .nbdesigner_modal_tab li span, 
#dg-product-info .nbdesigner_modal_tab li span {
    font-weight: 700
}

.nbdesigner_modal_tab li a:hover {
    color: #cc324b;
    text-decoration: none
}

.nb-col-2 {
    width: 20%
}

.nb-col-3 {
    width: 25%
}

.nb-col-4 {
    width: 33.33333333%
}

.nb-col-6 {
    width: 50%
}

.nb-col-8 {
    width: 66.66666666%
}

.nb-col-12 {
    width: 100%
}

.nb-col-30 {
    width: 30%
}

.nb-col-40 {
    width: 40%
}

.nb-col-60 {
    width: 60%
}

.nb-col-70 {
    width: 70%
}

.nb-col-2,
.nb-col-3,
.nb-col-4,
.nb-col-6,
.nb-col-8,
.nb-col-12,
.nb-col-30,
.nb-col-40,
.nb-col-60,
.nb-col-70 {
    float: left;
    padding: 0;
    margin: 0;
    padding-left: 5px
}

.nb-col-2:first-child,
.nb-col-3:first-child,
.nb-col-4:first-child,
.nb-col-6:first-child,
.nb-col-8:first-child,
.nb-col-12:first-child,
.nb-col-30:first-child,
.nb-col-40:first-child,
.nb-col-60:first-child,
.nb-col-70:first-child {
    padding-left: 0
}

fieldset {
    border: 1px solid #ddd;
    padding-left: 5px
}

legend {
    font-size: 11px;
    font-weight: 700;
    margin: 0;
    border: 0;
    padding-left: 5px
}

input.form-control:focus {
    border-color: #394264;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(57, 66, 100, 0.6);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(57, 66, 100, 0.6);
    -o-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(57, 66, 100, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(57, 66, 100, 0.6)
}

#main_menu {
    position: fixed;
    bottom: 20px;
    height: 40px;
    left: 0;
    z-index: 5;
    width: 100%
}

.shadow {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12)
}

.e-shadow {
    text-align: center;
    line-height: 25px;
    cursor: pointer;
    background: #fff;
    border-radius: 50%;
    color: #394264;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12)
}

.hover-shadow:hover {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15)
}

.e-hover-shadow:hover {
    color: #cc324b;
    text-shadow: 2px 2px 3px rgba(204, 50, 75, 0.5);
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15)
}

.container_menu {
    position: absolute;
    left: 20px;
    top: 0;
    width: 40px;
    height: 40px;
    background: #fff;
    overflow: hidden;
    border-radius: 50%
}

#menu {
    width: 20px;
    height: 20px;
    position: relative;
    margin: 10px auto;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: .5s ease-in-out;
    -moz-transition: .5s ease-in-out;
    -o-transition: .5s ease-in-out;
    transition: .5s ease-in-out;
    cursor: pointer
}

#menu span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: #394264;
    border-radius: 3px;
    opacity: 1;
    left: 0;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: .25s ease-in-out;
    -moz-transition: .25s ease-in-out;
    -o-transition: .25s ease-in-out;
    transition: .25s ease-in-out
}

#menu span:nth-child(1) {
    top: 0
}

#menu span:nth-child(2) {
    top: 8px
}

#menu span:nth-child(3) {
    top: 16px
}

#menu.open span:nth-child(1) {
    top: 8px;
    background: #cc324b;
    -webkit-transform: rotate(135deg);
    -moz-transform: rotate(135deg);
    -o-transform: rotate(135deg);
    transform: rotate(135deg)
}

#menu.open span:nth-child(2) {
    opacity: 0;
    background: #cc324b;
    left: -20px
}

#menu.open span:nth-child(3) {
    top: 8px;
    background: #cc324b;
    -webkit-transform: rotate(-135deg);
    -moz-transform: rotate(-135deg);
    -o-transform: rotate(-135deg);
    transform: rotate(-135deg)
}

.tool_draw {
    visibility: hidden;
    position: absolute;
    bottom: 40px;
    left: 23px
}

.tool_draw li,
.tool-right li {
    margin-bottom: 15px
}

.tool_draw li a,
.tool-right li span {
    display: inline-block;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    line-height: 33px;
    font-size: 20px;
    text-align: center;
    cursor: pointer;
    z-index: 4;
    opacity: 0;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    -webkit-transform: scaleY(0.4) scaleX(0.4) translateY(40px) translateX(0px);
    transform: scaleY(0.4) scaleX(1) translateY(40px) translateX(0px);
    background: #fff;
    position: relative;
    border: 2px solid #fff
}

.tool_draw.open,
.tool-right.open {
    visibility: visible
}

.tool_draw li a.menuUp,
.tool-right li span.menuUp {
    opacity: 1;
    -webkit-transform: scaleY(1) scaleX(1) translateY(0px) translateX(0px);
    -moz-transform: scaleY(1) scaleX(1) translateY(0px) translateX(0px);
    -ms-transform: scaleY(1) scaleX(1) translateY(0px) translateX(0px);
    -o-transform: scaleY(1) scaleX(1) translateY(0px) translateX(0px);
    transform: scaleY(1) scaleX(1) translateY(0px) translateX(0px);
}

.tool_draw li a i:before {
    color: #394264
}

.tool_draw li a:hover i:before {
    color: #cc324b
}

.tool_draw li a:hover {
    border: 2px solid #cc324b;
    box-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 20px #FF1177, 0 0 35px #FF1177, 0 0 40px #FF1177, 0 0 50px #FF1177, 0 0 75px #F17
}

.tool_draw li a .after {
    font-size: 10px;
    height: 16px;
    line-height: 15px;
    color: #394264;
    position: absolute;
    left: 45px;
    border: 1px solid #394264;
    border-radius: 8px;
    top: 9px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    white-space: nowrap;
    padding-left: 5px;
    padding-right: 5px;    
    -webkit-transition: linear 0.4s;
    -moz-transition: linear 0.4s;
    -ms-transition: linear 0.4s;
    transition: linear 0.4s;   
    -webkit-transform: translateX(15px);
    -moz-transform: translateX(15px);
    -ms-transform: translateX(15px);
    transform: translateX(15px);
}

.tool_draw li a:hover .after {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);    
}


#layer {
    position: absolute;
    left: 80px;
    top: 0;
    background: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    z-index: 6
}

#layer .layer_after,
#addition_tool:after,
#gesture .gesture_after,
#info .info_after {
    position: absolute;
    display: block;
    font-size: 14px;
    top: -25px;
    left: 0;
    color: #394264;
    text-shadow: 2px 2px 7px rgba(0, 0, 0, 0.4);
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s
}

#layer.open .layer_after,
#addition_tool.open:after,
#gesture.open .gesture_after {
    display: none
}

.nav_layer {
    width: 20px;
    height: 20px;
    margin: 10px auto;
    position: relative;
    cursor: pointer;
    overflow: hidden
}

.nav_layer span {
    width: 100%;
    height: 50%;
    position: absolute;
    background: #394264;
    -webkit-transform: skewX(30deg);
    -moz-transform: skewX(30deg);
    -o-transform: skewX(30deg);
    transform: skewX(30deg);
    border: 2px solid #fff;
    -webkit-transition: .25s ease-in-out;
    -moz-transition: .25s ease-in-out;
    -o-transition: .25s ease-in-out;
    transition: .25s ease-in-out;
    opacity: 1;
    left: 0;
    display: block
}

.nav_layer span:nth-child(1) {
    top: 0;
    z-index: 3
}

.nav_layer span:nth-child(2) {
    top: 6px;
    z-index: 2
}

.nav_layer span:nth-child(3) {
    top: 12px;
    z-index: 1
}

#layer.open .nav_layer {
    overflow: hidden
}

#layer.open .nav_layer span {
    height: 3px;
    background: #cc324b;
    border-radius: 3px;
    border: none
}

#layer.open .nav_layer span:nth-child(1) {
    top: 8px;
    -webkit-transform: rotate(135deg) skewX(0deg);
    -moz-transform: rotate(135deg) skewX(0deg);
    -o-transform: rotate(135deg) skewX(0deg);
    transform: rotate(135deg) skewX(0deg)
}

#layer.open .nav_layer span:nth-child(2) {
    opacity: 0;
    left: -20px
}

#layer.open .nav_layer span:nth-child(3) {
    top: 8px;
    -webkit-transform: rotate(-135deg) skewX(0deg);
    -moz-transform: rotate(-135deg) skewX(0deg);
    -o-transform: rotate(-135deg) skewX(0deg);
    transform: rotate(-135deg) skewX(0deg)
}

#gesture {
    position: absolute;
    left: 140px;
    top: 0;
    background: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    z-index: 8;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    line-height: 40px;
    text-align: center;
    font-size: 20px; 
    cursor: pointer;
}
.menu_gesture {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 50%
}

.menu_gesture span {
    width: 34px;
    height: 34px;
    line-height: 32px;
    text-align: center;
    color: #394264;
    border: 2px solid #fff;
    background: #fff;
    position: absolute;
    font-size: 15px;
    border-radius: 50%;
    z-index: 7;
    cursor: pointer;
    top: 3px;
    left: 3px;
    display: none;
    -webkit-transition: all .25s;
    -moz-transition: all .25s;
    -o-transition: all .25s;
    transition: all .25s;
    opacity: 0;
    visibility: hidden
}

.menu_gesture span:before {
    color: #394264
}

#gesture.open .menu_gesture span:hover {
    background: #fff;
    border-color: #cc324b;
    color: #cc324b
}

#gesture.open .menu_gesture span:hover:before {
    color: #cc324b
}

.rotate90 {
    -mstransform: rotate(90deg);
    -o-transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    transform: rotate(90deg)
}

#gesture.open .menu_gesture span {
    display: block
}

#gesture .menu_gesture span.m-center {
    display: block;
    opacity: 1;
    visibility: visible;
    font-size: 18px
}

#gesture.open .menu_gesture span.m-center {
    border: none;
    color: #cc324b
}

#gesture.open .menu_gesture span.m-center:before {
    color: #cc324b
}

#gesture.open .menu_gesture.open span {
    opacity: 1;
    visibility: visible
}

#gesture.open .menu_gesture.open span.left {
    top: 3px;
    left: -33px
}

#gesture.open .menu_gesture.open span.right {
    top: 3px;
    left: 39px
}

#gesture.open .menu_gesture.open span.up {
    top: -27.4px;
    left: -15px
}

#gesture.open .menu_gesture.open span.down {
    top: 33.4px;
    left: -15px
}

#gesture.open .menu_gesture.open span.flip-ver {
    top: -27.4px;
    left: 21px
}

#gesture.open .menu_gesture.open span.flip-hoz {
    top: 33.4px;
    left: 21px
}

#gesture.open .menu_gesture.open span.set-ver {
    top: 68.4px;
    left: -15px
}

#gesture.open .menu_gesture.open span.set-hoz {
    top: 68.4px;
    left: 21px
}

#gesture.open .menu_gesture.open span.delete {
    top: 68.4px;
    left: -87px;
    font-weight: 700
}

#gesture.open .menu_gesture.open span.refresh {
    top: 68.4px;
    left: -51px;
    font-weight: 700
}

#gesture.open .menu_gesture.open span.zoom-out {
    top: 68.4px;
    left: 57px
}

#gesture.open .menu_gesture.open span.zoom-in {
    top: 68.4px;
    left: 93px
}

#container_layer {
    position: fixed;
    z-index: 6;
    width: 240px;
    left: -250px;
    top: 50px;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    background: #fff;
    padding-bottom: 5px
}

#container_layer.open {
    left: 0
}

.close-popover {
    width: 25px;
    height: 25px;
    line-height: 24px;
    border: 1px solid #fff;
    border-radius: 50%;
    text-align: center;
    background: #fff;
    margin-top: -4px
}

.close-popover:hover {
    outline: none;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15)
}

.close-popover:before {
    color: #aaa
}

.close-popover:hover:before {
    color: #000
}

.popup h3 {
    border: none;
    border-radius: 0;
    background: #fff;
    color: #394264
}

#dg-layers {
    padding: 0 5px;
    background: #fff;
    border-radius: 0;
    max-height: 200px;
    overflow: hidden;
    position: relative
}

#dg-layers ul {
    margin: 0
}

#dg-layers ul li {
    padding-left: 10px;
    padding-right: 10px;
    cursor: move;
    border-left: 3px solid transparent;
    width: 100%;
    height: 44px;
    display: block;
    font-size: 10px;
    color: #394264;
    line-height: 44px
}

#dg-layers ul li:not(:last-child) {
    margin-bottom: 2px
}

#dg-layers ul li.active {
    border-left: 3px solid #cc324b;
    color: #cc324b;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15)
}

#dg-layers ul li:hover {
    border-left: 3px solid #cc324b;
    color: #cc324b
}

#dg-layers ul li:hover i:before {
    color: #cc324b
}

#dg-layers ul li i:before {
    font-size: 13px;
    color: #394264
}

h2.typeis-,
img.typeis-google,
span.typeis-image,
img.typeis-text,
i.typeis-image,
i.typeis-path-group,
img.typeis-path-group {
    display: none
}

img.layer_thumb {
    max-width: 100%;
    max-height: 100%
}

#addition_tool {
    position: absolute;
    top: 0;
    right: 20px;
    z-index: 5;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
    cursor: pointer
}

#addition_tool .tool-right {
    position: absolute;
    top: 3px;
    left: 3px;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    font-size: 18px;
    background: #fff;
    border: 2px solid #fff;
    opacity: 0;
    visibility: hidden;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    line-height: 34px;
    text-align: center
}

#addition_tool .tool-right li:hover {
    border: 2px solid #cc324b;
    color: #cc324b
}

#addition_tool.open .tool-right {
    opacity: 1;
    visibility: visible
}

#addition_tool .menu_right {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #394264;
    line-height: 40px;
    text-align: center;
    margin: 0;
    font-size: 20px
}

#addition_tool .menu_right:hover {
    color: #cc324b
}

#addition_tool.open .tool-right.layer_forward {
    top: -82px;
    left: 3px
}

#addition_tool.open .tool-right.layer_front {
    top: -66.6px;
    left: -37.5px
}

#addition_tool.open .tool-right.layer_back {
    top: -36.5px;
    left: -67.5px
}

#addition_tool.open .tool-right.layer_backward {
    left: -82px;
    top: 3px
}

/*#info {
    position: absolute;
    right: 20px;
    z-index: 5;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
    cursor: pointer
}*/
#info {
    position: absolute;
    right: 20px;
    z-index: 5;    
    height: 40px;
    border-radius: 22px;
    width: initial;
    padding: 0 15px;
    line-height: 38px;
/*    border: 2px solid #394264;*/
    font-weight: bold;
    box-sizing: content-box;
    box-shadow: 0 7px 8px -4px rgba(0,0,0,.2), 0 12px 17px 2px rgba(0,0,0,.14), 0 5px 22px 4px rgba(0,0,0,.12);
    background: #fff;
    
    cursor: pointer    
}
#info.disable {
    pointer-events: none;
}
#info:hover {
    box-shadow: 0 11px 15px -7px rgba(0,0,0,.2), 0 24px 38px 3px rgba(0,0,0,.14), 0 9px 46px 8px rgba(0,0,0,.12);
}
#info p {
    font-weight: bold;
    margin: 0;    
    display: inline-block;
    color: #394264;
    text-transform: uppercase;
}
#info p span {
    position: initial;
    visibility: visible;
    background: transparent;    
    opacity: 1;    
}
.container_info {
    position: relative
}

.container_info span {
    position: absolute;
    width: 38px;
    height: 38px;
    color: #394264;
    border-radius: 50%;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    line-height: 38px;
    text-align: center;
    font-size: 18px;
    background: #fff;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    top: 3px;
    left: 3px
}

.container_info.open span {
    opacity: 1;
    visibility: visible
}

.container_info.open span:hover {
    color: #cc324b;
    border-color: #cc324b
}

.container_info span.menu_cart {
    top: 1px;
    left: 1px;
    border: none!important;
    color: #394264;
    opacity: 1;
    visibility: visible
}

.container_info span.menu_cart:hover {
    color: #cc324b
}

.container_info.open span.add_cart {
    top: -43px;
    left: 3px
}

.container_info.open span.product_info {
    top: -32.5px;
    left: -32.5px
}

.container_info.open span.help {
    top: 3px;
    left: -43px
}

#tool-top {
    position: fixed;
    top: 5px;
    right: 20px;
    width: 34px;
    z-index: 5
}

#tool-top span {
    margin-bottom: 10px;
    display: block;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 34px;
    color: #394264
}

#designer-controller {
    position: relative;
    z-index: 2
}

.grid-area {
    display: none
}

.viewport {
    position: absolute;
    margin: 0 auto;
    text-align: center;
    top: 0;
/*    overflow: hidden*/
}

.view_container {
    width: 100%;
    height: 100%;
    position: relative
}
.design-image,
.grid-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}
.design-aria {
    position: absolute
}
.nbd-bleed, .nbd-safe-zone {
    position: absolute;
    pointer-events: none;
    box-sizing: border-box;
}
.nbd-bleed {
    border: 1px solid #ff0000;
}
.nbd-safe-zone {
    border: 1px dashed #00ff00;
}
.container-image {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 5px 8px 0 rgba(0,0,0,.14), 0 1px 14px 0 rgba(0,0,0,.12);
}

.container-image img {
    position: absolute;
    display: block
}

/*.design-aria {
    border: 1px dashed transparent
}*/

.design-aria:hover {
    box-shadow: 0 7px 8px -4px rgba(0,0,0,.2), 0 12px 17px 2px rgba(0,0,0,.14), 0 5px 22px 4px rgba(0,0,0,.12);
}
/*MD checkbox*/
.md-checkbox {
    position: relative;
    height: 20px;
    margin: 16px 0;
}
.md-checkbox label {
    cursor: pointer;
}
.md-checkbox label:before, .md-checkbox label:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
}
.md-checkbox label:before {
    width: 20px;
    height: 20px;
    background: #fff;
    border: 2px solid rgba(0, 0, 0, 0.54);
    border-radius: 2px;
    cursor: pointer;
    transition: background .3s;
}
.md-checkbox input[type="checkbox"] {
    outline: 0;
    margin-right: 10px;
}
.md-checkbox input[type="checkbox"]:checked + label:before {
    background: #394264;
    border: none;
}
.md-checkbox input[type="checkbox"]:checked + label:after {
    transform: rotate(-45deg);
    top: 5px;
    left: 4px;
    width: 12px;
    height: 6px;
    border: 2px solid #fff;
    border-top-style: none;
    border-right-style: none;
}
/*End. MD checkbox*/

/*MD raido */
@keyframes ripple {
  0% {
    box-shadow: 0px 0px 0px 1px transparent;
  }
  50% {
    box-shadow: 0px 0px 0px 15px rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow: 0px 0px 0px 15px transparent;
  }
}
.md-radio {
  margin: 16px 0;
}
.md-radio.md-radio-inline {
  display: inline-block;
}
.md-radio input[type="radio"] {
  display: none;
}
.md-radio input[type="radio"]:checked + label:before {
  border-color: #394264;
  animation: ripple 0.2s linear forwards;
}
.md-radio input[type="radio"]:checked + label:after {
  transform: scale(1);
}
.md-radio label {
  display: inline-block;
  height: 20px;
  position: relative;
  padding: 0 30px;
  margin-bottom: 0;
  cursor: pointer;
  vertical-align: middle;
}
.md-radio label:before, .md-radio label:after {
  position: absolute;
  content: '';
  border-radius: 50%;
  transition: all .3s ease;
  transition-property: transform, border-color;
}
.md-radio label:before {
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 0, 0, 0.54);
}
.md-radio label:after {
  top: 5px;
  left: 5px;
  width: 10px;
  height: 10px;
  transform: scale(0);
  background: #394264;
}
/*End. MD raido */
.nbd-bleed-notation {
    width: 20px;
    height: 0px;
    border-bottom: 2px solid #ff0000;
    display: inline-block;
    vertical-align: middle;
    margin-right: 15px;
}
.nbd-safe-zone-notation-box {
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    border: 2px dashed #00ff00;
    height: 20px;
    margin-right: 15px;
}
#frame {
    position: absolute;
    width: 190px;
    left: 50%;
    margin-left: -95px;
    height: 40px
}
.container_frame {
    position: relative;
    width: 100%;
    height: 100%
}
.container_frame span {
    position: absolute;
    top: 50%;
    height: 24px;
    width: 24px;
    margin-top: -12px;
    line-height: 23px;
    text-align: center;
    border-radius: 50%;
    color: #394264;
    z-index: 5;
    font-size: 16px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.5)
}
.container_frame span:hover {
    background: rgba(204, 50, 75, 0.3);
    color: #cc324b
}

.container_frame span.left {
    left: -15px
}

.container_frame span.right {
    right: -5px
}

.container-inner-frame {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.container_item {
    position: absolute;
    left: 0;
    top: 0;
    display: flex
}

.container_item .box-thumb {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 1px solid #394264;
    /*border-radius: 4px;*/
    margin: 0 10px 0 0;
    z-index: 4;
    cursor: pointer;
    overflow: hidden
}
.container_item .box-thumb i {
    display: inline-block;
    width: 40px;
    height: 40px;    
}
.container_item .box-thumb.active {
    border-color: #cc324b;
}

#od_config {
    position: fixed;
    bottom: 20px;
    width: 320px;
    left: 50%;
    margin-left: -160px;
    z-index: 8;
    min-height: 50px;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s
}

#od_config.mobile {
    bottom: -150px
}

#od_config.open {
    bottom: 20px
}

.nbdesigner_config {
    background: #fff;
    position: relative
}

#od_config.mobile .hide-config {
    position: absolute;
    bottom: -15px;
    margin-left: -12px;
    left: 50%;
    z-index: 9
}

.od_tab,
.od_tabs {
    border-radius: 0!important;
    padding: 0!important
}

.od_tab > ul,
.od_tabs > ul {
    background: none!important;
    border: none!important;
    border-radius: 0!important;
    padding: 0 10px!important
}

.od_tab > ul > li {
    width: 50px!important;
    margin: 0!important;
    padding: 0!important
}

.od_tab > ul > li > a {
    padding: 0!important;
    display: block!important;
    text-decoration: none!important;
    width: 100%!important;
    height: 50px!important;
    line-height: 50px!important;
    text-align: center!important;
    font-size: 20px!important;
    color: #394264
}

.od_tab ul li a:focus,
.od_tab ul li a:hover {
    outline: none;
    color: #cc324b;
    text-shadow: 2px 2px 3px rgba(204, 50, 75, 0.5)
}

.od_tab .list-indicator {
    position: absolute;
    bottom: 0;
    width: 50px;
    height: 3px;
    background: #cc324b;
    left: 0;
    padding: 0;
    margin: 0
}

#config_text div.content,
#config_image div.content,
#config_art div.content,
#config_draw div.content {
    position: absolute;
    bottom: 50px;
    padding: 10px;
    max-height: 100px;
    background: #fff;
    border-radius: 0;
    width: 100%
}

#typography textarea.form-control {
    padding: 3px;
    font-size: 12px;
    color: #394264;
    border-radius: 0
}

#typography fieldset {
    cursor: pointer
}

.item-config {
    width: 25px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    cursor: pointer;
    display: inline-block;
    margin-right: 3px;
    margin-bottom: 5px;
    float: left
}

.item-config:hover {
    color: #cc324b
}

.input-config {
    width: 45px;
    height: 25px;
    border: 0;
    padding: 3px;
    font-size: 13px!important
}

.label-config {
    font-size: 10px;
    font-weight: 700;
    padding: 0;
    margin: 0;
    color: #394264
}

.label-config.multi {
    margin-top: 15px
}

.jscolor {
    border: none;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    font-size: 0!important;
    cursor: pointer!important;
    float: left;
    margin-right: 5px;
    cursor: pointer
}

.jscolor:focus,
.input-config:focus {
    outline: none
}

.pattern {
    background: url(../images/sprites.png) 0 0
}

#rotate-curved {
    padding-bottom: 0!important
}

.text-item {
    padding: 3px 6px;
    height: 20px;
    font-size: 10px;
    border-radius: 10px;
    margin-bottom: 5px;
    margin-right: 3px;
    cursor: pointer;
    color: #394264;
    font-weight: 700
}

.text-item:hover {
    color: #cc324b
}

.c-option {
    width: 25px;
    height: 25px
}

.popup-option {
    background: #fff;
    position: absolute;
    bottom: 45px;
    border-bottom: 1px solid #ddd;
    z-index: 9;
    visibility: hidden;
/*    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;*/
    transition: all .4s;
/*    -moz-transform: scale(0);
    -webkit-transform: scale(0);
    -o-transform: scale(0);*/
    transform: scale(0);
    opacity: 0;
    left: 0;
    padding: 10px
}

.popup-option.open {
    width: 320px;
    visibility: visible;
/*    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -o-transform: scale(1);*/
    transform: scale(1);
    opacity: 1
}

.popup-option .after {
    opacity: 0;
    position: absolute;
    bottom: -5px;
    width: 10px;
    height: 10px;
    border: 1px solid #ddd;
    border-left-color: transparent;
    border-top-color: transparent;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    background: #fff
}

.popup-option.open .after {
    opacity: 1
}

.toggle-popup-option.open:before {
    content: "\f077"
}

.has-popover-option {
    position: relative
}

#helpdesk {
    position: fixed;
    top: 0;
    right: -320px;
    width: 300px;
    z-index: 6;
    background: #fff;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    height: 100%;
    bottom: 0
}

#helpdesk.open {
    right: 0
}

#helpdesk .inner-help {
    overflow: hidden;
    position: relative
}

#helpdesk h3 {
    margin: 0;
    padding: 15px 10px;
    font-size: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
    color: #394264
}

#helpdesk .close-helpdesk {
    position: absolute;
    top: 5px;
    right: 0;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    cursor: pointer
}

#helpdesk .inner-help ul li {
    display: inline-block;
    margin: 0;
    padding: 0;
    border-radius: 0;
/*    width: 85px*/
    border-right: 1px solid #ddd;
    background: rgba(0,0,0,0.05);
}

#helpdesk .inner-help ul li a {
    color: #394264;
    padding: 5px;
    cursor: pointer;
    text-transform: uppercase;
    font-size: 11px;
    text-align: center;
    width: 100%
}

#helpdesk .inner-help ul li.ui-state-active a {
    color: #fff;
    background: #394264;
    width: 100%;
    text-align: center
}

#helpdesk .inner-help ul li:hover a{
    color: #fff;
    background: #394264;
}

#helpdesk .inner-help ul li a:focus {
    outline: none
}

#helpdesk .inner-help > div {
    padding: 10px;
    font-size: 10px;
    max-height: 600px;
    overflow: hidden;
    position: relative
}

#helpdesk .inner-help > div img {
    border-bottom: 1px solid #ddd;
    display: block;
    margin-bottom: 10px;
    max-width: 100%
}
#shortcuts .shortkey {
    display: inline-block;
    font-size: 12px;    
}
#shortcuts .shortkey.left {
    width: 40%;
    padding-right: 15px;
    text-align: right;
}
#shortcuts .shortkey.right {
    width: 50%;
}
#shortcuts .shortkey.left .key{
    display: inline-block;
    padding-left: 2px;
    height: 20px;
    border: 1px solid #ddd;
    text-align: center;
    line-height: 20px;
    padding-right: 2px;
    border-radius: 3px;
    color: #242729;
    font-size: 11px;
    margin-left: 5px;
    min-width: 22px;
    box-shadow: 0 1px 0 rgba(12,13,14,0.2), 0 0 0 2px #FFF inset;
    background-color: #e1e3e5;
}
#shortcuts .shortkey.left .key.long {
    padding-left: 5px;    
    padding-right: 5px;    
}
#upload-tabs li,
.nbdesigner_modal_tab li{
    display: inline-block
}

.nbdesigner_image_url {
    border-radius: 0;
    display: inline-block;
    height: 30px;
    max-width: 300px;
    margin-left: 15px
}
.nbdesigner_image_url[type="number"] {
    padding-right: 0;
}
#nbdesigner_preloader {
    position: relative;
    left: 50%;
    width: 40px;
    height: 40px;
    top: 50%;
    margin-top: -40px;
    margin-left: -20px;    
}

#nbdesigner_preloader span {
    display: block;
    bottom: 0;
    width: 20px;
    height: 20px;
    background: #f98332;
    position: absolute
}

#nbdesigner_preloader span:nth-child(1) {
    animation: nbdesigner_preloader_1 1.5s infinite ease-in-out;
    -webkit-animation: nbdesigner_preloader_1 1.5s infinite ease-in-out
}

#nbdesigner_preloader span:nth-child(2) {
    left: 20px;
    animation: nbdesigner_preloader_2 1.5s infinite ease-in-out;
    -webkit-animation: nbdesigner_preloader_2 1.5s infinite ease-in-out
}

#nbdesigner_preloader span:nth-child(3) {
    top: 0;
    animation: nbdesigner_preloader_3 1.5s infinite ease-in-out;
    -webkit-animation: nbdesigner_preloader_3 1.5s infinite ease-in-out
}

#nbdesigner_preloader span:nth-child(4) {
    top: 0;
    left: 20px;
    animation: nbdesigner_preloader_4 1.5s infinite ease-in-out;
    -webkit-animation: nbdesigner_preloader_4 1.5s infinite ease-in-out
}

@-webkit-keyframes nbdesigner_preloader_1 {
    0% {
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        transform: translateX(-20px) translateY(-10px) rotate(-180deg);
        border-radius: 20px;
        background: #3498db;
        -webkit-transform: translateX(-20px) translateY(-10px) rotate(-180deg);
        border-radius: 20px;
        background: #3498db
    }
    80% {
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
    100% {
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
}

@keyframes nbdesigner_preloader_1 {
    0% {
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        transform: translateX(-20px) translateY(-10px) rotate(-180deg);
        border-radius: 20px;
        background: #3498db;
        -webkit-transform: translateX(-20px) translateY(-10px) rotate(-180deg);
        border-radius: 20px;
        background: #3498db
    }
    80% {
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
    100% {
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
}

@-webkit-keyframes nbdesigner_preloader_2 {
    0% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        -webkit-transform: translateX(20px) translateY(-10px) rotate(180deg);
        border-radius: 20px;
        background: #f1c40f;
        transform: translateX(20px) translateY(-10px) rotate(180deg);
        border-radius: 20px;
        background: #f1c40f
    }
    80% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
    100% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
}

@keyframes nbdesigner_preloader_2 {
    0% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        -webkit-transform: translateX(20px) translateY(-10px) rotate(180deg);
        border-radius: 20px;
        background: #f1c40f;
        transform: translateX(20px) translateY(-10px) rotate(180deg);
        border-radius: 20px;
        background: #f1c40f
    }
    80% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
    100% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
}

@-webkit-keyframes nbdesigner_preloader_3 {
    0% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        -webkit-transform: translateX(-20px) translateY(10px) rotate(-180deg);
        border-radius: 20px;
        background: #2ecc71;
        transform: translateX(-20px) translateY(10px) rotate(-180deg);
        border-radius: 20px;
        background: #2ecc71
    }
    80% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
    100% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
}

@keyframes nbdesigner_preloader_3 {
    0% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        -webkit-transform: translateX(-20px) translateY(10px) rotate(-180deg);
        border-radius: 20px;
        background: #2ecc71;
        transform: translateX(-20px) translateY(10px) rotate(-180deg);
        border-radius: 20px;
        background: #2ecc71
    }
    80% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
    100% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(-360deg);
        border-radius: 0
    }
}

@-webkit-keyframes nbdesigner_preloader_4 {
    0% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        -webkit-transform: translateX(20px) translateY(10px) rotate(180deg);
        border-radius: 20px;
        background: #e74c3c;
        transform: translateX(20px) translateY(10px) rotate(180deg);
        border-radius: 20px;
        background: #e74c3c
    }
    80% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
    100% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
}

@keyframes nbdesigner_preloader_4 {
    0% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(0deg);
        border-radius: 0
    }
    50% {
        -webkit-transform: translateX(20px) translateY(10px) rotate(180deg);
        border-radius: 20px;
        background: #e74c3c;
        transform: translateX(20px) translateY(10px) rotate(180deg);
        border-radius: 20px;
        background: #e74c3c
    }
    80% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
    100% {
        -webkit-transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0;
        transform: translateX(0px) translateY(0px) rotate(360deg);
        border-radius: 0
    }
}

.od_processing {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background: #fff;
    opacity: 0;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s
}
.od_processing p {
    top: 50%;
    position: relative;
    text-align: center;
    margin-top: 10px;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px
}
.design-options, #upload-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9998;
    background-color: #f1f1f1;
}
.design-options .inner, .upload-container .inner {
    display: flex;
    align-items: center;
    justify-content: center;    
    width: 100%;
    height: 100%;
    text-align: center;
}
.design-options .option {
    padding: 20px;
    display: inline-block;
    text-transform: uppercase;
    background-color: #fff;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 5px 8px 0 rgba(0,0,0,.14), 0 1px 14px 0 rgba(0,0,0,.12);
}
.design-mode {
    opacity: 0;
    pointer-events: none;
}
.design-mode.active {
    opacity: 1;
    pointer-events: all;
}
.design-options .option:not(:last-child) {
    margin-right: 15px;
    margin-bottom: 15px;
}
.design-options .option i {
    font-size: 20px;
    margin-right: 15px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    border: 1px solid #aaa;   
}
.design-options .option:hover {
    box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2), 0 6px 20px 0 rgba(0,0,0,0.19);
}
.design-options .option:active {
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 5px 8px 0 rgba(0,0,0,.14), 0 1px 14px 0 rgba(0,0,0,.12);
}
.nbdesigner_upload {
    background: #394264;
    border: none;
    border-radius: 0;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s
}

.nbdesigner_upload:hover,
.nbdesigner_upload:focus {
    outline: none;
    background: #394264;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)
}

.nbdesigner_upload_image {
    height: 100px;
    display: inline-block;
    border-radius: 0;
    cursor: pointer;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 0
}

#dag-files-images {
    padding: 0 15px;
    max-height: 350px;
    overflow: hidden;
    position: relative
}

#uploaded-facebook, #dropbox_images {
    margin-top: 15px;
    max-height: 300px;
    overflow: hidden;
    position: relative
}

.view-thumb {
    display: inline-block;
    position: relative
}

.spinner {
    position: absolute;
    left: 0;
    top: 0;
    background: url(images/ajax-loader.gif) center center/32px 32px no-repeat;
    display: block;
    width: 100%;
    height: 100%
}

.progress {
    margin-top: 20px;
    border-radius: 0
}

.filter1:before {
    color: #e64c65
}

.filter2:before {
    color: #11a8ab
}

.filter3:before {
    color: #3468af
}

.filter4:before {
    color: #f58e29
}

.has_divider {
    margin-left: 5px;
    border-left: 1px solid #ddd
}

.nbdesigner_mg5 {
    margin-top: 5px;
    padding: 0
}

.blend_mode_option button,
#free_draw button,
#draw_shape button {
    padding: 5px;
    font-size: 11px;
    display: block;
    margin: 0 auto;
    border: none;
    border-radius: 0;
    background: #394264!important;
    margin-top: 5px;
    margin-left: 15px!important
}

#free_draw button,
#draw_shape button {
    margin-top: 1px;
    margin-left: 0!important
}

.blend_mode_option ul li a,
#free_draw ul li a,
#draw_shape ul li a {
    font-size: 11px
}

.blend_mode_option ul li a:hover,
#free_draw ul li a:hover,
#draw_shape ul li a:hover {
    color: #fff;
    background: #394264;
    text-shadow: none
}

.blend_mode_option ul {
    background: #fff!important;
    border-radius: 0!important;
    border: none!important;
    top: -10px!important;
    border: none;
    left: 70px
}

#free_draw ul,
#draw_shape ul {
    background: #fff!important;
    border-radius: 0!important;
    border: none!important;
    border: none;
    margin-bottom: 3px
}

.shap_mark {
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: inline-block;
    margin-right: 5px
}

.shap_mark:last-child {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg)
}

.container_crop_canvas {
    position: relative;
    overflow: hidden;
    height: 400px;
    padding: 0!important
}

.nbdesigner_config_svg {
    padding: 15px!important;
    max-height: 150px;
    overflow: hidden
}

.nbdesigner_config_svg input {
    margin-bottom: 10px;
    margin-right: 10px
}

#dg-crop-image .modal-footer {
    padding: 10px 15px!important;
    height: 50px
}

#dg-crop-image .modal-footer .nbdesigner_button {
    margin: 2px!important
}

.container_crop_canvas .canvas-container {
    margin: 0 auto
}

#dg-config-art .modal-dialog {
    position: absolute;
    bottom: 0;
    left: 50%;
    margin-left: -300px
}

.modal-backdrop.in {
    opacity: .3
}

.nbdesigner_modal .modal-body {
    padding: 15px 0
}

.nbdesigner_modal h5 {
    margin: 0;
    font-weight: 700
}

.nbdesigner_textarea {
    border-radius: 0
}

.nbdesigner_modal .modal-header button.close {
    margin-top: -20px
}

.nbdesigner_modal .modal-header button.close:focus {
    outline: none
}

.nbdesigner_modal .nbdesigner_button {
    background: #394264;
    border: none;
    border-radius: 0;
    padding: 5px;
    color: #fff;
    margin: 10px 0;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s;
    cursor: pointer
}

.nbdesigner_modal .nbdesigner_button:focus {
    outline: none
}

.qr_image {
    width: 200px;
    height: 200px;
    padding: 10px;
    border: 1px solid #ddd;
    cursor: pointer;
    display: block;
    margin: 0 auto
}

.nbdesigner_art_modal_header {
    text-align: center;
    padding-bottom: 5px
}

.nbdesigner_art_modal_header > span {
    font-weight: 700;
    float: left;
    margin-top: 5px
}

.nbdesigner_art_modal_header input {
    display: inline-block;
    max-width: 200px;
    height: 30px;
    border-radius: 0
}

#dg-cliparts .modal-body {
    padding: 15px!important
}

.nbdesigner_art_modal_header button.dropdown-toggle,
.nbdesigner_art_modal_header button.dropdown-toggle:focus {
    height: 30px;
    border-radius: 0;
    background: #394264;
    border: none
}

.nbdesigner_art_modal_header .open button.dropdown-toggle {
    background: #394264
}

.nbdesigner_art_modal_header ul.dropdown-menu {
    border-radius: 0;
    border: none;
    margin-top: 5px;
    text-align: left
}

.nbdesigner_art_modal_header ul.dropdown-menu li a:hover {
    background: #394264
}

#nbdesigner_art_container {
    max-height: 350px;
    position: relative;
    overflow: hidden
}

#svg_path {
    max-height: 150px;
    overflow: hidden;
    position: relative
}

#svg_path .jscolor {
    margin-bottom: 5px
}

.nbdesigner_thumb {
    min-width: 50px
}

#dg-fonts .modal-body {
    padding: 15px
}

#nbdesigner_font_container {
    max-height: 350px;
    position: relative;
    overflow: hidden;
    font-size: 0
}
.nbdesigner_font {
    height: 30px;
    display: inline-block;
    position: relative;
    margin-bottom: 10px;
    margin-right: 10px;
    overflow: hidden;
    opacity: .3;
    text-align: center;
    padding: 3px;
    font-size: 16px
}

.nbdesigner_font .spinner {
    background-color: rgba(255, 255, 255, 0.7)
}

.nbdesigner_font a {
    color: #394264
}

.nbdesigner_font a:hover {
    color: #394264;
    text-shadow: 2px 3px 7px rgba(57, 66, 102, 0.6)
}

.dropdown-menu .divider {
    margin: 4px 0
}

img.pattern_img {
    display: inline-block;
    width: 50px;
    height: 50px;
    margin: 0 10px 10px 0;
    border: 1px solid #ddd;
    cursor: pointer
}

#pattern-boddy {
    max-height: 400px;
    overflow: hidden
}

.disabledraw, .deactive-group {
    background: #cc324b!important;
    color: #fff!important
}

#draw_shape {
    padding-bottom: 0!important
}

.label-rotate {
    padding-left: 10px
}

#dg-preview .modal-body {
    max-height: 400px
}

#dg-preview .modal-body .img_preview {
    height: 350px;
    display: block;
    margin: 0 auto
}

.glass {
    width: 200px;
    height: 200px;
    position: absolute;
    border-radius: 50%;
    cursor: crosshair;
    box-shadow: 0 0 0 7px rgba(255, 255, 255, 0.85), 0 0 7px 7px rgba(0, 0, 0, 0.25), inset 0 0 40px 2px rgba(0, 0, 0, 0.25);
    display: none;
    z-index: 9999
}

.first_message {
    position: fixed;
    right: 65px;
    top: 5px;
    width: 200px;
    background: #fff;
    border-radius: 3px;
    padding: 5px 10px;
    font-size: 10px;
    color: #394264;
    box-shadow: 0 6px 13px 0 rgba(0, 0, 0, .23);
    line-height: 15px;
    cursor: pointer;
    -webkit-animation: first_message 1.5s infinite ease-in-out;
    -moz-animation: first_message 1.5s infinite ease-in-out;
    -o-animation: first_message 1.5s infinite ease-in-out;
    animation: first_message 1.5s infinite ease-in-out;
    z-index: 99
}
.translate-switch {
    position: fixed;
    right: 65px;
    top: 40px;
    background: #fff;
    -webkit-transition : all 0.3s; 
    -moz-transition: all 0.3s; 
    -o-transition: all 0.3s; 
    transition: all 0.3s; 
    z-index: 100;
    font-size: 12px;
    color: #394264;
    visibility: hidden;
    opacity: 0;
    width: 100px;
    border-radius: 4px; 
    transform-style: preserve-3d;
    transform: rotateY(-90deg);    
}
.translate-switch.open {
    transform: rotateY(0deg);  
    visibility: visible;
    opacity: 1;
}
.translate-switch ul {
    margin: 0;
}
.translate-switch li {
    padding: 5px;
    -webkit-transition : all 0.3s; 
    -moz-transition: all 0.3s; 
    -o-transition: all 0.3s; 
    transition: all 0.3s;     
}
.translate-switch li:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.translate-switch li:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}
.translate-switch li:hover, .translate-switch li.open {
    cursor: pointer;
    background: #394264;
    color: #fff;
}
.translate-switch li:not(:last-child) {
    border-bottom: 1px solid #ddd;
}
.translate-switch:after {
    position: absolute;
    top: 18px;
    right: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid #fff;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    content: '';
/*    box-shadow: 0 6px 13px 0 rgba(0, 0, 0, .23);*/
}
@-webkit-keyframes first_message {
    0% {
        opacity: .5
    }
    50% {
        opacity: 1
    }
    100% {
        opacity: .5
    }
}

@keyframes first_message {
    0% {
        opacity: .5
    }
    50% {
        opacity: 1
    }
    100% {
        opacity: .5
    }
}

.first_message:after {
    position: absolute;
    top: 13px;
    right: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid #fff;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    content: '';
/*    box-shadow: 0 6px 13px 0 rgba(0, 0, 0, .23)*/
}

.first_visitor {
    background: #f17!important;
    color: #fff!important
}

.full_screen_preview {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 34px;
    height: 34px;
    text-align: center;
    line-height: 34px;
    border-radius: 50%;
    cursor: pointer;
    background: rgba(60, 180, 216, 0.6);
    font-size: 20px;
    opacity: .7;
    -webkit-transition: all .4s;
    -moz-transition: all .4s;
    -o-transition: all .4s;
    transition: all .4s
}

.full_screen_preview:hover {
    opacity: 1
}

#od_config.modepc .hide-tool-config {
    position: absolute;
    margin-left: -12px;
    left: 50%;
    bottom: -12px
}

.processing {
    position: absolute;
    bottom: 5px;
    left: 50%;
    width: 50px;
    height: 50px;
    margin-left: -25px;
    z-index: 11
}

.nbdesigner_lock_layer:hover i:before {
    color: #f98332!important
}

#dg-layers ul li.lock {
    -moz-opacity: .6;
    opacity: .6
}

#container_layer h3 {
    font-weight: bold;
    font-size: 14px;
    padding-left: 5px;
    margin-top: 10px;
    border-bottom: 1px solid #ddd;
    line-height: 30px;
    padding-right: 5px;
    position: relative;
    padding-bottom: 10px;
}
#container_layer h3:after {
    display: none
}
#container_layer h3 .fa-refresh {
    cursor: pointer;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 26px;
    border-radius: 50%;
    border: 2px solid #394264;
}
#container_layer h3 span span {
    cursor: pointer
}

#_close_popover {
    display: none
}

#container_layer .container-dg-slider {
    margin-top: 15px;
    padding: 5px 20px 5px 15px;
    border-top: 1px solid #ddd
}
.toggle-upload {
    position: fixed;
    top: 5px;
    right: 65px;
    height: 34px;
    line-height: 34px;
    font-weight: bold;
    background: #fff;
    padding: 0 15px;
    cursor: pointer;
    color: #394264;
    z-index: 5;
}
.toggle-upload:hover {
    box-shadow: 0 4px 10px 0 rgba(0,0,0,0.2), 0 4px 20px 0 rgba(0,0,0,0.19);    
}
.tooltip {
    z-index: 99999999;
}
.nbd-prevent-events {
    pointer-events: none;
}
@media screen and (-ms-high-contrast: active),
(-ms-high-contrast: none) {
    #nbdesigner_art_container .nbdesigner_thumb {
        max-width: 100px;
        margin-left: 10px;
        margin-bottom: 10px;
        width: 100px;
        height: 100px
    }
    #nbdesigner_art_container .nbdesigner_upload_image {
        max-width: 100px;
        max-height: 100px;
        height: auto
    }
}

.delete-all {
    margin-left: 7px;
}

#dg-expand-feature .modal-body {
    padding: 15px;
}
.nbd-list-cart-design, .nbd-list-my-design {
    max-height: 400px;
    overflow: hidden;
    position: relative; 
}
.design-editor-container, .nbdesigner-list-template{   
    max-height: 350px;
    overflow: hidden;
    visibility: hidden;
    height: 0px;
    opacity: 0;
    -webkit-transition-property: height, opacity;
    -moz-transition-property: height, opacity;
    -ms-transition-property: height, opacity;
    -o-transition-property: height, opacity;
    transition-property: height, opacity;
    -webkit-transition-duration: 0.4s;
    -moz-transition-duration: 0.4s;
    -ms-transition-duration: 0.4s;
    -o-transition-duration: 0.4s;
    transition-duration: 0.4s;
    -webkit-transition-delay: 0s, .15s;
    -moz-transition-delay: 0s, .15s;
    -ms-transition-delay: 0s, .15s;
    -o-transition-delay: 0s, .15s;
    transition-delay: 0s, .15s;
}
.design-editor-container.open, .nbdesigner-list-template.open, .nbd-list-product {
    display: block;
    opacity: 1;
    height: 400px;
    visibility: visible;
}
#product-variation-wrap {
    max-height: 350px;
    overflow: hidden;
    position: relative;    
}
.nbd-description-title {
    text-transform: uppercase;
    text-decoration: underline;    
}
.design-editor-container textarea{
    height: auto;
    width: 100%;
}
.feature-button {
    width: 36px;
    height: 36px;
    margin-right: 15px;
    margin-bottom: 15px;
    border-radius: 50%;
    line-height: 36px;
    font-size: 19px;
    display: inline-block;
    text-align: center;
    cursor: pointer;
    color: #394264;
}
.btn-dialog {
    margin: 15px 0;
}
#container_layer h3 span span {
    cursor: pointer;
    font-size: 13px;
    color: #394264;
    background: #394264;  
    width: 30px;
    height: 30px;
    line-height: 29px;
    border: none;
    border-radius: 50%;
    text-align: center;
    margin-top: -4px;
}
#container_layer h3 span span:before {
    color: #fff;
}
#container_layer h3 span span:hover{
    background: #1dd9bb
}
.nbdesigner-template {
    width: 135px;
    display: inline-block;
    margin-right: 7px;
    margin-top: 10px;
    border: 1px solid #ddd;
    cursor: pointer;
}
.nbd-product-wrap, .nbd-template-wrap {
    width: 200px;
    display: inline-block;
    margin-right: 7px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    -webkit-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    -moz-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    -ms-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    transition: all 0.4s;   
    vertical-align: top;
}
.nbd-product-wrap:hover, .nbd-template-wrap:hover {
    -webkit-box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);
    -moz-box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);
    -ms-box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);
    box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);    
}
.nbd-product-wrap img, .nbd-template-wrap img {
    display: block;
    max-width: 100%;
    border-radius: 0;  
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    transition: all 0.4s;  
    cursor: pointer;    
}
.nbd-product-wrap:hover img {
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
}
.nbd-product-img{
    overflow: hidden;
}
.nbd-product-info {
    height: 50px;
    border-top: 1px solid #ddd;
}
.nbd-product-info p.product-name{
    vertical-align: top;
    line-height: 28px;
    padding: 10px;
    width: calc(100% - 50px);
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;   
    margin: 0;
}
.product-action {
    width: 50px;
    height: 50px;
    position: relative;
    display: inline-block;
    float: right;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    transition: all 0.4s;      
}
.product-action:hover {
    background: #ddd;
}
.product-action span {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    line-height: 50px;
    text-align: center;
    width: 50px;
    height: 50px;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    color: #394264;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    transition: all 0.4s;    
    font-size: 16px;
}
.product-action span.active {
    display: flex;
}
.nbd-product-search {
    border-radius: 30px;
    border: 1px solid #ddd;
    line-height: 30px; 
    padding: 0 15px;
}
.nbd-product-search:focus {
    border: 1px solid rgba(51, 51, 51,0.7);
    outline: none;
}
.product-search {
    margin-bottom: 15px;
}
.nbdesigner-list-template h3{
    font-size: 14px;
    color: #394264;
    margin: 0 0 15px 0;
}
.nbd-disable-event {
    pointer-events: none !important;
}
.nbdesigner-list-template, .nbd-list-product{   
    position: relative;
    overflow: hidden;
}
#config-style {
    position: fixed;
    top : 50px;
    width: 210px;
    left: -210px;
    z-index: 5;
    background: #fff;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    -o-transition: all 0.4s;
    transition: all 0.4s;
}
#config-style.open {
    left: 0;
}
#config-style .config-style-con {
    position: relative;
}
#config-style .config-style-con .bg-style {
    position: relative;
    overflow: hidden;
    max-height: 240px;
/*    font-size: 0;*/
}
#config-style .config-style-con #toggle-config-style {
    position: absolute;
    top: 0;
    right: -30px;
    width: 30px;
    height: 30px;
    background: #fff;
    color: #394264;
    display: block;
    cursor: pointer;
    font-size: 20px;
    text-align: center;
    line-height: 30px;
    opacity: 0.5;
}
#config-style h3 {
    font-size: 13px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 15px;
    padding-left: 5px;
    padding-top: 15px;
    color: #394264;
    margin: 0 0 10px 0;
}
.bg-style-tem, .background-none {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 1px solid #ddd;
    margin-left: 5px;
    margin-bottom: 5px;
    cursor: pointer;
}
.background-none {
    position: relative;
    vertical-align: top;
}
.background-none:after {
    width: 25px;
    height: 1px;
    position: absolute;
    display: block;
    transform: rotate(45deg);
    transform-origin: top left;
    background: red;
    top: 0;
    left: 0;
    content: '';    
}
.con-webcam {
    width: 400px;
    height: 300px;
    margin: 0 auto;
    text-align: center;
}
.con-webcam img {
    margin: 0 auto;
    max-width: 100%;
    max-height: 100%;
}
.con-webcam.off {
    padding-top: 40px;
}
.icon-camera {
    color: #ddd;
    font-size: 200px;
    line-height: 200px;
    display: inline-block;
    border: 1px dashed #ddd;
    border-radius: 15px;
    opacity: 0.6;
    padding: 20px;
}
.btn-primary[disabled] {
    background-color: #ddd;
}
#nbdesigner_webcam button {
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    -o-transition: all 0.4s;
    transition: all 0.4s;
}
.top-center-menu {
    height: 30px;
    background: transparent;
    z-index: 6;
    position: fixed;
    text-align: center;
    top: 5px;
    font-size: 0;
}
.toolbar-menu {
    width: 30px;
    height: 30px;
    display: inline-block;
    background: #fff;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    color: #394264;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    transition: all 0.4s;
    font-size: 14px;
    vertical-align: top;
}
.toolbar-menu:not(:last-child) {
    margin-right: 3px;
}
.toolbar-menu.active {
    color: #cc324b;
    border-color: #cc324b;    
}
.toolbar-menu:hover {
    background: #394264;
    color: #fff;
    border-color: #394264;
}
.toolbar-menu.undo-redo {
    pointer-events: none;
}
.toolbar-menu.undo-redo:before {
    opacity: 0.3;    
}
.toolbar-menu.undo-redo.ready:before {
    opacity: 1;    
}
.toolbar-menu.undo-redo.ready {
    pointer-events: all;
    opacity: 1;    
}
.toolbar-menu.download-file {
    font-size: 25px;
}
/* Tooltipster */
.tooltipster-base {
    pointer-events: all !important;
}
.tooltipster-sidetip.tooltipster-borderless .tooltipster-box {
    border: none;
    background: rgba(255, 255, 255, .5);
    border-radius: 0;
}

.tooltipster-sidetip.tooltipster-borderless.tooltipster-bottom .tooltipster-box {
    margin-top: 4px;
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-left .tooltipster-box {
    margin-right: 8px
}

.tooltipster-sidetip.tooltipster-borderless.tooltipster-right .tooltipster-box {
    margin-left: 8px
}

.tooltipster-sidetip.tooltipster-borderless.tooltipster-top .tooltipster-box {
    margin-bottom: 8px
}
.tooltipster-sidetip.tooltipster-borderless .tooltipster-arrow {
    height: 6px;
    margin-left: -4px;
    width: 12px;
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-left .tooltipster-arrow,
.tooltipster-sidetip.tooltipster-borderless.tooltipster-right .tooltipster-arrow {
    height: 16px;
    margin-left: 0;
    margin-top: -8px;
    width: 8px
}
.tooltipster-sidetip.tooltipster-borderless .tooltipster-arrow-background {
    display: none
}
.tooltipster-sidetip.tooltipster-borderless .tooltipster-arrow-border {
    border: 6px solid transparent
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-bottom .tooltipster-arrow-border {
    border-bottom-color: #1b1b1b;
    border-bottom-color: rgba(255, 255, 255, .5)
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-left .tooltipster-arrow-border {
    border-left-color: #1b1b1b;
    border-left-color: rgba(10, 10, 10, .9)
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-right .tooltipster-arrow-border {
    border-right-color: #1b1b1b;
    border-right-color: rgba(10, 10, 10, .9)
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-top .tooltipster-arrow-border {
    border-top-color: #1b1b1b;
    border-top-color: rgba(10, 10, 10, .9)
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-bottom .tooltipster-arrow-uncropped {
    top: -6px
}
.tooltipster-sidetip.tooltipster-borderless.tooltipster-right .tooltipster-arrow-uncropped {
    top: -6px;
}
.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow {
    top: -2px;
}
/* End. Tooltipster */
.canvas-container .upper-canvas.has-grid {
    background-color: transparent;
    background-image: linear-gradient(0deg, transparent 22%, rgba(0, 0, 0, 0.3) 24%, rgba(0, 0, 0, 0.3) 25%, transparent 27%, transparent 72%, rgba(0, 0, 0, 0.3) 74%, rgba(0, 0, 0, 0.3) 75%, transparent 77%, transparent), linear-gradient(90deg, transparent 22%, rgba(0, 0, 0, 0.3) 24%, rgba(0, 0, 0, 0.3) 25%, transparent 27%, transparent 72%, rgba(0, 0, 0, 0.3) 74%, rgba(0, 0, 0, 0.3) 75%, transparent 77%, transparent);
    background-size: 20px 20px;
    background-position: -4px -5px;     
}
#replace-element-upload {
    position: absolute;
    left: -100px;
    top: -100px;
    width: 34px;
    height: 34px;
    background: transparent;
    z-index: 6;
}
#replace-element-upload i{
    display: block;
    width: 100%;
    height: 100%;
    cursor: pointer;
    text-align: center;
    line-height: 34px;
    border-radius: 50%;
    border: 1px solid #394264;
    color: #394264;
    background: #fff; 
}
.background-transparent {
    background-image: -webkit-linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4), -webkit-linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4);
    background-image: -moz-linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4), -webkit-linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4);
    background-image: -o-linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4), -webkit-linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4);
    background-image: linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4), linear-gradient(45deg, #c4c4c4 25%, transparent 25%, transparent 75%, #c4c4c4 75%, #c4c4c4);
    background-position: 0 0, 10px 10px;
    -webkit-background-size: 20px 20px;
    background-size: 20px 20px;    
/*    border: 1px solid #394264;*/
}
.nbd-cat.dropdown-menu>li>a {
    position: relative;
    padding: 3px 50px 3px 20px;
}
.nbd-cat.dropdown-menu>li>a span {
    position: absolute;
    right: 20px;  
}
.nbdesigner-term a {
    color: #000;
    font-weight: bold;
    text-decoration: underline;
    vertical-align: top;    
}
[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
  display: none !important;
}
.side-name {
    font-weight: bold;
    box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 5px 8px 0 rgba(0,0,0,.14), 0 1px 14px 0 rgba(0,0,0,.12);
/*    border: 1px solid #394264;*/
    color: #394264;   
    padding: 5px 10px;
    background: #fff;
}
.side-navigator {
    position: fixed;
    top: 10px;
    left: 50px;     
}
.side-navigator .side-nav {
    height: 32px;
    line-height: 32px;
    width: 20px;
    text-align: center;
    cursor: pointer;
    opacity: 0.3;
    pointer-events: none;
}
.side-navigator .side-nav.ready {
    opacity: 1;
    pointer-events: all;    
}
.nbd-mode-1 .side-navigator {
    left: 40px; 
    top: 5px;
}
.pop-bg-color {
    position: fixed;
    top: 50px;
    right: 65px;
    width: 220px;
    background: #fff;
    z-index: 99;
    display: none;
}
.pop-bg-color.active {
    display: block;
}
.background-options {
    padding: 15px;
    font-size: 0;
}
.background-opt {
    background-image: url('../images/bg.jpg') !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
}
.layer.static {
    pointer-events: none;
}
.nbd-pop-handle {
    padding: 15px;
    border-bottom: 1px solid #ddd;
}
.pop-tools {
    position: fixed;
    top: 50px;
    left: 250px;
    width: 80px;
    background: #fff;
    z-index: 99;
    display: none;
}
.pop-tools.active {
    display: block;     
}
.pop-tools h2, .pop-bg-color h2{
    font-size: 14px;
    width: 100%;
    height: 30px;
    margin: 0;
    line-height: 30px;
    cursor: move;
    border-bottom: 1px solid #ddd;
    padding-left: 5px;
    color: #394264;
}
.pop-tools h2 i {
    opacity: 0.7;
}
.pop-tools h2 span{
    padding-left: 5px;
    width: 60px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
}
.pop-tools .tools-con {
    width: 100%;
}
.pop-tools .tools-con span {
    width: 40px;
    height: 40px;
    display: inline-block;
    margin: 0;
    box-shadow: none;
    text-align: center;
    line-height: 38px;
    cursor: pointer;
    float: left;
    box-sizing: border-box;
    color: #394264;
}
.pop-tools .tools-con span:hover {
    color: #cc324b;
}
.pop-tools .tools-con > span span:hover svg {
    fill: #cc324b;
}
.pop-tools .tools-con span span {
    width: 20px; 
    height: 20px; 
    padding: 10px; 
    color: #394264;
    box-sizing: content-box;
}
.pop-tools .tools-con > span span svg {
    fill: #394264;
}
.nbd-upload-items {
    width: 150px;
    height: 150px;
    display: inline-block;
    max-width: 500px;
    max-height: 350px;
    margin: 15px;
}
.nbd-upload-item {
    max-width: 100%;
    max-height: 100%;
}
.inputfile {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;    
}
.inputfile + label {
    padding: 6px 12px;
    font-size: 14px;
    color: white;
    background-color: #394264;
    display: inline-block;
    cursor: pointer;
    padding: 5px 10px;
}
.inputfile + label svg {
    width: 1em;
    height: 1em;
    vertical-align: middle;
    fill: currentColor;
    margin-top: -0.25em;
    margin-right: 0.25em;
}
.inputfile:focus + label,
.inputfile + label:hover {
    background-color: rgba(57,66,100,0.7);
}
#upload-computer .inputfile + label:hover {
    background-color: #394264;
}
.nbd-upload-items-inner {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;    
}
.nbd-upload-items-inner span {
    position: absolute;
    z-index: 2;
    width: 30px;
    height: 30px;
    cursor: pointer;
    background: #fff;
    line-height: 30px;
    -webkit-transform: translateY(30px);    
    -moz-transform: translateY(30px);    
    transform: translateY(30px); 
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
    border-radius: 50%; 
    font-size: 20px;
    color: #394264;  
    color: #cc324b;
}
.nbd-upload-items-inner:hover span {
    -webkit-transform: translateY(-10px);    
    -moz-transform: translateY(-10px);    
    transform: translateY(-10px);     
}
.upload-design-preview {
    margin: 15px;
    max-height: 300px;
    max-width: 720px;
    position: relative;
    overflow: hidden;    
}
.file-upload-name {
    width: 300px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin: 15px auto;
}
.upload-container h2 {
    margin-bottom: 20px;
}
.submit-upload-design {
    height: 40px;
    border-radius: 20px;
    background: #fff;
    padding: 0 15px;
    color: #394264;
    text-transform: uppercase;
    font-weight: bold;
    line-height: 40px;
    cursor: pointer;
    display: inline-block;
    margin-top: 15px;  
    box-shadow: 0 5px 6px -3px rgba(0,0,0,.2), 0 9px 12px 1px rgba(0,0,0,.14), 0 3px 16px 2px rgba(0,0,0,.12); 
}
.submit-upload-design:hover {
    box-shadow: 0 11px 15px -7px rgba(0,0,0,.2), 0 24px 38px 3px rgba(0,0,0,.14), 0 9px 46px 8px rgba(0,0,0,.12);
}
.tooltipster-right .tooltipster-box, .tooltipster-top .tooltipster-box {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    background: #fff !important;
}
.tooltipster-right .tooltipster-arrow, .tooltipster-top .tooltipster-arrow {
    display: none;
}
.nbd-upload-item-title {
    position: absolute;
    border: 0;
    background: #fff;
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-overflow: ellipsis;
    overflow: hidden;
    padding: 0 5px;
    white-space: nowrap;   
    font-weight: bold;
}
.back-to-main-site {
    position: fixed;
    top: 10px; left: 10px;
    z-index: 10;
    background: #fff;
    width: 34px;
    height: 34px;
    line-height: 34px;
    cursor: pointer;
    border-radius: 50%;
    color: #394264;
    text-align: center;
    font-size: 20px;    
}
.custom-dimension {
    border: 2px solid #fff;
}
.custom-dimension svg {
    width: 16px;
    height: 16px;
    margin-top: 7px;
    fill: #394264
}
.custom-dimension.active {
    border: 2px solid #cc324b;
    box-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 20px #FF1177, 0 0 35px #FF1177, 0 0 40px #FF1177, 0 0 50px #FF1177, 0 0 75px #F17;
}
.custom-dimension.active svg {
    fill: #cc324b;
}
.sp-replacer, .sp-container, .sp-container button {
    border: none !important;
    background: #fff !important;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12) !important;
}
.sp-container input {
    border-radius: 0 !important;
    border: 1px solid #ddd !important;  
}
.sp-container input:focus{
    border-color: #394264;
    outline: none;
}
.sp-container button {
    border-radius: 0 !important;
}
.sp-container button:hover {
    background: #394264 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15) !important;
    color: #fff !important;
}
.sp-container button:focus {
    outline: none !important;
}
.sp-color, .sp-hue, .sp-clear {
    border: none !important;
}
.nbd-dropdown-option button{
    border-radius: 0;
    background: #394264!important;    
    border: none;
}
.nbd-dropdown-option ul {
    background: #fff!important;
    border-radius: 0!important;
    border: none!important;    
}
.nbd-dropdown-option ul li a {
    text-shadow: none;    
}
.nbd-dropdown-option ul li a:hover {
    color: #fff;
    background: #394264;    
    outline: none;
    color: #fff;
    text-shadow: 2px 2px 3px rgba(204, 50, 75, 0.5);
}
#dg-custom-dimension .nbd-dropdown-option ul li a:hover {
    text-shadow: none;
}
.nbd-rounded {
/*    border-radius: 50%;*/
}
#toolbar-menu-handle {
    cursor: move;
    background: #fff;
    color: #ddd;
    display: none;
}
.top-center-menu:hover #toolbar-menu-handle {
    display: inline-block;
}
.nbdesigner_svg_code {
    width: 500px;
    border-radius: 0;
    display: inline-block;
    margin-left: 15px;    
}
.background-opt .sp-replacer {
    width: 100%;
    height: 100%;
    padding: 0;
    border-radius: 50%;
    text-align: center;    
}
.background-opt .sp-preview {
    margin: 0;
    border: none;
    width: 100%;
    height: 100%;    
}
.background-opt .sp-dd {
    display: none;
}
.stage-background {
    position: fixed;
    top: 50px;
    left: 250px;
    width: 300px;
    background: #fff;
    z-index: 99;
    opacity: 0;  
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
}
.nbd-color-item {
    width: 30px; 
    height: 30px;
    margin: 0 10px 10px 0;
    display: inline-block;
    border-radius: 50%; 
    cursor: pointer;
    border: 1px solid #ddd;
}
.nbd-color-item:nth-child(5n){
    margin-right: 0;
}
.nbd-remove-stage-bg{
    font-size: 20px;
    line-height: 30px;
    vertical-align: top;
    text-align: center;
    background: #cc324b;
    color: #fff;
    margin-left: 15px;    
}
.nbd-flat-btn {
    border: 1px solid #ddd;
    line-height: 34px;
}
.nbd-flat-btn:hover {
    border: 1px solid #fff;
}
.progress-bar {
    background-color: #394264;
    line-height: 20px;
}
.picker-dialog {
    z-index: 999999 !important;
}
#nbdesigner_url label {
    min-width: 150px;
}
.variations td {
    padding: 0;
}
.variations select {
    background: #fff url('../images/select-arrows.png') no-repeat 100% 50%;
    border: 1px solid #ccc;
    padding: 0.5em 0.75em;
    padding-right: 2em;
    -webkit-appearance: button;    
    -moz-appearance: button;    
    -ms-appearance: button;  
    margin-bottom: 15px;
    height: 36px;
    cursor: pointer;
}
.variations select:focus {
    outline: none;
}
.variations .label label {
    text-transform: capitalize;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    color: #000;    
    min-width: 150px;
    text-align: left;    
}
.variations .reset_variations {
    display: inline-block;
    margin-left: 15px;
    height: 36px;
    line-height: 34px;
    border: 1px solid #ddd;
    vertical-align: middle;
    padding: 0 1em;  
    color: rgba(57, 66, 100, 0.7);
    font-weight: bold;   
    border-radius: 18px;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    -ms-transition: all 0.4s;
    transition: all 0.4s;
}
.variations .reset_variations:hover {
    color: #cc324b;
    border-color: #cc324b;
}
.variations .reset_variations:before {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\f021";
    margin-right: 15px;    
}
.text-case svg {
    margin-top: 5px;
}
.text-case svg path {
    fill: #394264;
}
.text-case:hover svg path {
    fill: #cc324b;
}
.product-info-wrap {
    position: relative;
    max-height: 350px;
    overflow: hidden;
    margin-bottom: 15px;
}
.nbd-info-wrap {
    margin-bottom: 15px;
    padding-right: 15px;
}
.nbd-info-wrap-inner {
    clear: both;
    overflow: hidden;    
}
.nbd-info {
    float: left;
    width: 50%;
    display: inline-block;
    margin: 0;
    overflow: hidden;
}
.nbd-info img {
    max-width: 100%;
}
.pro-description {
    margin-top: 15px;
    clear: both;
}
.pro-description table {
    width: 100%;
    outline: none;
    border: 0.0625rem solid #ddd;  
}
.pro-description table tr:nth-child(odd) {
    background-color: #f6fbfc;
}
.pro-description table td {
    vertical-align: top;
    padding: 0.75rem 1.125rem;
    outline: none;
    border-bottom: 0.0625rem solid #ddd;
    text-align: left;
    font-weight: normal;    
}
.pro-description table td:first-child {
    width: 33%;
}
.nbd-info p {
    text-align: justify;
}
.nbd-info:nth-child(2){
    padding-left: 15px;
}
.nbd-info h3 {
    margin-top: 0;
}
.box-thumb-inner {
    margin: 0;
    padding: 0;
    border: none;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;    
}
.box-thumb-inner img{
    max-width: 100%;
    max-height: 100%;
}
#pixabay_results, #unsplash_results {
    max-height: 350px;
    overflow: hidden;
    position: relative;
}
#pixabay_loading{
    display: none;
    margin-top: 10px;
}
.preview-thumbnail-wrap {
    position: fixed;
    width: 100px;
    right: 65px;
    bottom: 100px;
    cursor: pointer;
    max-height: 210px;
    z-index: 98;
}
.preview-thumbnail-item {
    width: 100px;
    height: 100px;
    margin-bottom: 5px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    position: relative; 
    box-sizing: content-box;
}
.preview-thumbnail-item.active {
    border: 1px solid #394264;
}
.preview-thumbnail-inner {
    position: absolute;
}
.preview-thumbnail-item span {
    display: inline-block;
    width: 100%;
    height: 100%;
}
.preview-thumbnail-item img {
    width: 100%;
    height: 100%; 
}
.thumbnail-nav {
    position: absolute;
    top: -15px;
    left: 50%;
    margin-left: -15px;
    width: 30px;
    height: 30px;
    text-align: center;
    border-radius: 100%;
    background: #fff;
    line-height: 30px;
    cursor: pointer;
    z-index: 2;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);    
    opacity: 0;
    pointer-events: none;    
}
.thumbnail-nav.ready {
    opacity: 1;
    pointer-events: all;    
}
.thumbnail-nav.next {
    bottom: -15px;
    top: unset;
}
.printing-line {
    position: fixed;
    top: 50px;
    right: 65px;
    z-index: 98;
    background: #fff;
    padding: 15px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05), 0 2px 10px 0 rgba(0, 0, 0, 0.05); 
}
.layer-color-option {
    position: fixed;
    top: 50px;
    right: 65px;
    width: 250px;
    background: #fff;
    z-index: 99;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05), 0 2px 10px 0 rgba(0, 0, 0, 0.05); 
}
.layer-color-option h2 {
    font-size: 14px;
    width: 100%;
    height: 30px;
    margin: 0;
    line-height: 30px;
    cursor: move;
    border-bottom: 1px solid #ddd;
    padding-left: 5px;
    color: #394264;
}
.add-layer-color {
    font-size: 14px;
    height: 28px;
    line-height: 28px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.05), 0 2px 10px 0 rgba(0, 0, 0, 0.05);
    display: inline-block;
    margin-left: 5px;
    padding-left: 5px;
    padding-right: 5px;
    vertical-align: top;
    background: #394264;
    color: #fff;
    cursor: pointer;
    -webkit-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
}
.nbd-list-layer-color {
    width: 70px;
    display: inline-block;
    height: 20px;
    vertical-align: top;
    border-radius: 2px;
    position: relative;
    font-size: 10px;
    line-height: 20px;
    margin-bottom: 5px;
    margin-right: 5px;
    color: #fff;
    cursor: pointer;
}
.nbd-list-layer-color:nth-child(3n){
    margin-right: 0;
}
.nbd-list-layer-color span {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    color: red;
    background: rgba(255,255,255,0.85);
    border: 1px solid #394264;
    width: 20px;
    height: 20px;
    text-align: center;
    font-size: 20px;
}
.add-layer-color:hover {
    outline: none;
    background: #394264;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);    
}
.color_link_group {
    margin-top: 10px;
    font-size: 10px;
    padding-top: 5px;
    border-top: 1px solid #ddd;    
}
.gg-font-preview-wrap-inner {
    overflow: hidden;
    margin-left: -5px;
    margin-right: -5px;    
}
.gg-font-preview {
    width: 25%; 
    margin: 0;
    display: inline-block;
    float: left;
    cursor: pointer;
    opacity: 1;
}
.gg-font-preview.disable {
    pointer-events: none;
    opacity: 0.3;
}
.gg-font-preview-inner-wrap {
    padding: 7px;
}
.gg-font-preview-inner {
    padding: 5px 10px;
    -webkit-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    -moz-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    -ms-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    background: #fff;
    font-size: 14px;
    -webkit-transition:  all 0.4s;
    -moz-transition:  all 0.4s;
    transition:  all 0.4s;
}
.gg-font-preview-inner p {
    margin: 0;
}
.gg-font-preview:nth-child(4n+1) {
    clear: both;
}
.nbd-swatch {
    background-repeat: no-repeat;
    background-size: cover;    
}
@media (max-width: 767px) {
    .gg-font-preview {
        width: 50%;
    }
    .preview-thumbnail-wrap, .printing-line {
        display: none;
    }
    #upload-tabs li {
        display: inline-block;
        margin-bottom: 3px;
        min-width: 45px
    }
    .nbdesigner_upload_image {
        height: 50px
    }
    #dag-files-images,
    #nbdesigner_art_container,
    #nbdesigner_font_container,
    #pattern-boddy {
        max-height: 300px
    }
    #uploaded-facebook {
        max-height: 250px
    }
    .nbdesigner_art_modal_header input {
        margin-bottom: 10px
    }
    .nbdesigner_art_modal_header .btn-group {
        vertical-align: top
    }
    .modal.in .modal-dialog {
        margin-top: 40px
    }
    #tool-top {
        right: 5px
    }
    .first_message {
        display: none
    }
    .nbdesigner_crop_footer {
        text-align: left;
        height: 95px
    }
    #dg-config-art .modal-dialog {
        margin-left: calc(10px - 50%)
    }
    #helpdesk .inner-help > div {
        max-height: 400px
    }
    button.pick_from_gd {
        margin-top: 15px;
    }
    .nbdesigner_image_url, .nbdesigner_svg_code {
        max-width: 100%;
        margin-left: 0
    }
    #container_layer h3 #_close_popover {
        display: none;
        position: absolute;
        top: -10px;
        right: -30px;
        height: 37px;
        width: 30px;
        background: #fff;
        text-align: center;
        line-height: 37px;
        cursor: pointer
    }
    #container_layer:hover h3 #_close_popover {
        display: block;
    }
    .tool_draw li {
        display: inline-block;
        margin-right: 15px;        
    }
    .tool_draw {
        bottom: 50px;
    }
    .tool_draw li a:hover .after {
        top: -35px;
        left: -20px;
    }
    .design-options .option {
        display: block;
        margin-right: 0 !important;
    }
    .nbd-dropdown-option {
        margin-left: 0 !important;
    }
    .nbd-insert-page-type {
        display: block;
        margin-left: 15px !important;
    }
    .nbd-insert-page-position {
        margin-left: 15px !important;
    }
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
    #typography textarea.form-control {
        font-size: 16px !important;
    }
}