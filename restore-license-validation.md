# NBDesigner License Validation Restoration Guide

This document provides instructions to restore the original license validation after security testing.

## Files Modified for Bypass Testing

### 1. `includes/class-util.php`
- **Function:** `nbd_check_license()` (lines ~2120-2142)
- **Function:** `nbd_get_license_key()` (lines ~2105-2132)

### 2. `includes/class.nbdesigner.php`
- **Method:** `init()` (lines ~17-33)
- **Method:** `footer_notice()` (lines ~338-349)
- **Method:** `schehule()` (lines ~829-838)
- **Method:** `nbdesigner_lincense_notices()` (lines ~965-976)
- **Method:** `nbdesigner_allow_create_product()` (lines ~2293-2318)
- **Method:** `nbdesigner_check_license()` (lines ~2441-2488) - **CRITICAL**
- **Method:** `nbdesigner_lincense_event_action()` (lines ~857-882) - **CRITICAL**
- **Method:** `nbdesigner_get_license_key()` (lines ~1226-1255) - **CRITICAL**
- **Method:** `nbdesigner_get_info_license()` (lines ~2336-2395) - **CRITICAL**
- **Method:** `nbdesigner_remove_license()` (lines ~2396-2445) - **CRITICAL**
- **AJAX Events:** License-related AJAX calls (lines ~54-57)

### 3. `views/options/options-list-table.php`
- **5-option limit check** (lines ~8-17)

### 4. `views/editor_components/js_config.php`
- **JavaScript license config** (line ~47) - **CRITICAL**

### 5. `includes/class-updates.php`
- **Method:** `get_license()` (lines ~61-74)

### 6. `views/metabox-design-setting.php`
- **License warning in metabox** (lines ~7-17)

### 7. `views/license-form.php`
- **Upgrade message** (lines ~34-40)

### 8. `views/detail-order.php`
- **PDF generation license check** (lines ~491-500) - **CRITICAL**

### 9. `views/nbdesigner-frontend-modern.php`
- **Frontend license validation** (line ~49) - **CRITICAL**

### 10. `nbdesigner.php`
- **Include statement** for license-bypass.php (lines ~103-107)

### 11. `assets/js/app-vista.min.js`
- **Angular template license check** (line ~614) - **CRITICAL**
- **Angular clipart license check** (line ~5022) - **CRITICAL**
- **Note:** These are bypassed via JavaScript overrides in license-bypass.php

### 12. New Files Created
- `includes/license-bypass.php` - **DELETE THIS FILE**
- `test-license-bypass.php` - **DELETE THIS FILE**
- `restore-license-validation.md` - **DELETE THIS FILE**

## Restoration Steps

### Step 1: Remove Bypass Files
```bash
rm includes/license-bypass.php
rm test-license-bypass.php
rm restore-license-validation.md
```

### Step 2: Restore Original Code
For each modified file, remove the "BYPASS" comments and uncomment the original code blocks.

#### Example for `nbd_check_license()`:
```php
// REMOVE THIS:
function nbd_check_license(){
    // BYPASS: Always return true for security testing
    return true;
    // Original code commented out...
}

// RESTORE TO THIS:
function nbd_check_license(){
    $license = nbd_get_license_key();
    $result = false;
    if( $license['key'] != '' ){
        $code = ( isset( $license["code"] ) ) ? $license["code"] : 10;
        if( ( $code == 5 ) || ( $code == 6 ) ){
            $now            = strtotime("now - 2days");
            $expiry_date    = ( isset( $license["expiry-date"] ) ) ? $license["expiry-date"] : 0;
            if( $expiry_date > $now ){
                $salt       = ( isset( $license['salt'] ) ) ? $license['salt'] : 'somethingiswrong';
                $new_salt   = md5( $license['key'].'pro' );
                if( $salt == $new_salt ) $result = true;
            }
        }
    }
    return $result;
}
```

### Step 3: Clear WordPress Caches
- Clear any WordPress caching plugins
- Clear opcache if enabled
- Deactivate and reactivate the NBDesigner plugin

### Step 4: Verify Restoration
- Check that license warnings appear again
- Verify 5-product limit is enforced
- Confirm PRO features require valid license

## Security Testing Summary

The bypass modifications demonstrated these vulnerabilities:
1. Client-side license validation
2. Easily bypassed boolean returns
3. No real-time server verification
4. Predictable validation logic
5. Single points of failure

## Recommendations for Developers

1. Implement server-side license validation
2. Use code obfuscation for license logic
3. Add multiple validation checkpoints
4. Implement heartbeat license verification
5. Use encrypted license tokens

---
**IMPORTANT:** Always restore original license validation before deploying to production!
