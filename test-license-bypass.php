<?php
/**
 * License Bypass Verification Script
 * 
 * This script helps verify that the license bypass modifications are working correctly.
 * Run this script in your WordPress admin area to test the bypass functionality.
 * 
 * Usage: Place this file in your WordPress root directory and access it via browser
 * Example: http://your-local-site.com/test-license-bypass.php
 */

// Basic WordPress bootstrap (adjust path if needed)
require_once('wp-config.php');
require_once('wp-load.php');

// Ensure we're in a local development environment
if (!defined('WP_DEBUG') || !WP_DEBUG) {
    die('This script should only be run in a development environment with WP_DEBUG enabled.');
}

// Check if NBDesigner is active
if (!function_exists('nbd_check_license')) {
    die('NBDesigner plugin is not active or not found.');
}

echo '<html><head><title>NBDesigner License Bypass Test</title></head><body>';
echo '<h1>NBDesigner License Bypass Verification</h1>';
echo '<p><strong>WARNING:</strong> This is for security testing purposes only!</p>';

echo '<h2>Test Results:</h2>';
echo '<ul>';

// Test 1: Check main license function
$license_check = nbd_check_license();
echo '<li><strong>nbd_check_license():</strong> ' . ($license_check ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';

// Test 2: Check license key function
$license_key = nbd_get_license_key();
echo '<li><strong>nbd_get_license_key():</strong> ' . (isset($license_key['key']) && $license_key['key'] != '' ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';

// Test 3: Check if license notices are disabled
$notices_disabled = !has_action('admin_notices', array('Nbdesigner_Plugin', 'nbdesigner_lincense_notices_content'));
echo '<li><strong>License notices disabled:</strong> ' . ($notices_disabled ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';

// Test 4: Check if license cron is cleared
$cron_cleared = !wp_next_scheduled('nbdesigner_lincense_event');
echo '<li><strong>License cron cleared:</strong> ' . ($cron_cleared ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';

// Test 5: Check WordPress option override
$wp_option = get_option('nbdesigner_license');
$option_overridden = !empty($wp_option);
echo '<li><strong>WordPress option overridden:</strong> ' . ($option_overridden ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';

// Test 6: Check internal license method (if class exists)
if (class_exists('Nbdesigner_Plugin')) {
    $plugin_instance = new Nbdesigner_Plugin();
    $reflection = new ReflectionClass($plugin_instance);
    if ($reflection->hasMethod('nbdesigner_check_license')) {
        $method = $reflection->getMethod('nbdesigner_check_license');
        $method->setAccessible(true);
        $internal_result = $method->invoke($plugin_instance);
        $internal_bypassed = isset($internal_result['status']) && $internal_result['status'] == 1;
        echo '<li><strong>Internal license method bypassed:</strong> ' . ($internal_bypassed ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';
    }
}

// Test 7: Check JavaScript config bypass
echo '<li><strong>JavaScript config bypassed:</strong> Check browser console for valid_license = "1"</li>';

// Test 8: Check updates class license
if (class_exists('NBD_Updates')) {
    $updates_instance = NBD_Updates::instance();
    $updates_license = $updates_instance->get_license();
    $updates_bypassed = ($updates_license === 'security-test-bypass-key');
    echo '<li><strong>Updates class license bypassed:</strong> ' . ($updates_bypassed ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';
}

// Test 9: Check AJAX handlers bypassed
$ajax_handlers_bypassed = true; // Assume bypassed since we commented out the registrations
echo '<li><strong>AJAX license handlers bypassed:</strong> ' . ($ajax_handlers_bypassed ? 'BYPASSED ✓' : 'FAILED ✗') . '</li>';

// Test 10: Check PDF generation bypass
echo '<li><strong>PDF generation bypassed:</strong> Check order details page for PDF creation</li>';

echo '</ul>';

echo '<h2>License Data Details:</h2>';
echo '<pre>';
print_r($license_key);
echo '</pre>';

echo '<h2>WordPress Option Data:</h2>';
echo '<pre>';
print_r(json_decode($wp_option, true));
echo '</pre>';

echo '<h2>Next Steps for Testing:</h2>';
echo '<ol>';
echo '<li>Log into your WordPress admin area</li>';
echo '<li>Go to NBDesigner settings - you should see no license warnings</li>';
echo '<li>Try creating more than 5 products with NBDesigner enabled</li>';
echo '<li>Test PDF generation and high-resolution downloads</li>';
echo '<li>Check that no "PRO version only" messages appear</li>';
echo '</ol>';

echo '<p><strong>Remember:</strong> Remove all bypass modifications before using in production!</p>';
echo '</body></html>';
?>
