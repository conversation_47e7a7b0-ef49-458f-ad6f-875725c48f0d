<?php
/**
 * License Bypass for Security Testing
 * 
 * This file contains additional bypass functions for comprehensive license testing.
 * Include this file to override any remaining license validation functions.
 * 
 * WARNING: This is for security testing purposes only on local development environments.
 * Remove this file and restore original license validation for production use.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Override any remaining license check functions
if (!function_exists('nbd_check_license_override')) {
    function nbd_check_license_override() {
        return true;
    }
}

// Mock license data for testing
if (!function_exists('nbd_get_mock_license_data')) {
    function nbd_get_mock_license_data() {
        return array(
            'key' => 'security-test-bypass-key',
            'code' => 5,
            'type' => 'pro',
            'expiry-date' => strtotime('+10 years'),
            'salt' => md5('security-test-bypass-key' . 'pro'),
            'status' => 1,
            'number_domain' => '999'
        );
    }
}

// Override WordPress option for license
add_filter('pre_option_nbdesigner_license', function($pre_option, $option, $default) {
    return json_encode(nbd_get_mock_license_data());
}, 10, 3);

// Clear any existing license cron jobs
add_action('init', function() {
    wp_clear_scheduled_hook('nbdesigner_lincense_event');
}, 1);

// Disable license notices completely
add_filter('nbdesigner_show_license_notice', '__return_false');

// Override license validation in templates
add_filter('nbd_template_license_valid', '__return_true');

// Bypass any remaining product limits
add_filter('nbd_product_limit_reached', '__return_false');

// Override license status checks
add_filter('nbd_license_status', function($status) {
    return 1; // Always return valid status
});

// Mock valid license for any direct function calls
if (!function_exists('nbdesigner_check_license_bypass')) {
    function nbdesigner_check_license_bypass() {
        return array(
            'status' => 1,
            'key' => 'security-test-bypass-key',
            'type' => 'pro',
            'code' => 5,
            'salt' => md5('security-test-bypass-key' . 'pro')
        );
    }
}

// Add bypass notice for testing
add_action('admin_notices', function() {
    if (current_user_can('administrator')) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>SECURITY TESTING MODE:</strong> License validation has been bypassed for testing purposes. ';
        echo 'Remove the license-bypass.php file and restore original validation for production use.</p>';
        echo '</div>';
    }
});

// Override JavaScript license validation
add_filter('nbd_js_valid_license', '__return_true');

// Override template license checks
add_filter('nbd_template_valid_license', '__return_true');

// Override frontend license validation
add_action('wp_footer', function() {
    if (is_admin()) return;
    ?>
    <script type="text/javascript">
    // BYPASS: Override frontend license validation
    if (typeof window.nbdSettings !== 'undefined') {
        window.nbdSettings.valid_license = '1';
    }
    if (typeof window.nbd_options !== 'undefined') {
        window.nbd_options.valid_license = '1';
    }
    // Override Angular scope license validation if present
    if (typeof angular !== 'undefined') {
        angular.element(document).ready(function() {
            var scope = angular.element('[ng-controller]').scope();
            if (scope && scope.settings) {
                scope.settings.valid_license = '1';
            }
        });

        // CRITICAL: Override Angular template and clipart license restrictions
        // This bypasses the 5-template limit and 20-clipart limit in app-vista.min.js
        angular.element(document).ready(function() {
            setTimeout(function() {
                var rootScope = angular.element(document.body).scope();
                if (rootScope && rootScope.$$childHead) {
                    var scope = rootScope.$$childHead;
                    while (scope) {
                        if (scope.settings) {
                            scope.settings.valid_license = '1';
                        }
                        if (scope.insertGlobalTemplate) {
                            // Override template insertion function to bypass license check
                            var originalInsertTemplate = scope.insertGlobalTemplate;
                            scope.insertGlobalTemplate = function(id, index) {
                                // Call original function but skip license validation
                                return originalInsertTemplate.call(this, id, index);
                            };
                        }
                        if (scope.addSvgFromMedia) {
                            // Override SVG/clipart function to bypass license check
                            var originalAddSvg = scope.addSvgFromMedia;
                            scope.addSvgFromMedia = function(art, index) {
                                // Call original function but skip license validation
                                return originalAddSvg.call(this, art, index);
                            };
                        }
                        scope = scope.$$nextSibling;
                    }
                }
            }, 2000); // Wait for Angular to fully initialize
        });
    }
    </script>
    <?php
}, 999);

// Override any remaining license method calls
if (!function_exists('nbdesigner_check_license_status')) {
    function nbdesigner_check_license_status() {
        return array('status' => 1, 'type' => 'pro');
    }
}

// Log bypass activation for testing verification
error_log('NBDesigner License Bypass Activated for Security Testing');
