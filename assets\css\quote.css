.nbdq_datepicker .ui-draggable-handle {
    -ms-touch-action: none;
    touch-action: none;
}
/* Layout helpers
----------------------------------*/
.nbdq_datepicker .ui-helper-hidden {
    display: none;
}
.nbdq_datepicker .ui-helper-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}
.nbdq_datepicker .ui-helper-reset {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    line-height: 1.3;
    text-decoration: none;
    font-size: 100%;
    list-style: none;
}
.nbdq_datepicker .ui-helper-clearfix:before,
.nbdq_datepicker .ui-helper-clearfix:after {
    content: "";
    display: table;
    border-collapse: collapse;
}
.nbdq_datepicker .ui-helper-clearfix:after {
    clear: both;
}
.nbdq_datepicker .ui-helper-zfix {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0;
    filter:Alpha(Opacity=0); /* support: IE8 */
}

.nbdq_datepicker .ui-front {
    z-index: 100;
}


/* Interaction Cues
----------------------------------*/
.nbdq_datepicker .ui-state-disabled {
    cursor: default !important;
    pointer-events: none;
}


/* Icons
----------------------------------*/
.nbdq_datepicker .ui-icon {
    display: inline-block;
    vertical-align: middle;
    margin-top: -.25em;
    position: relative;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat;
}

.nbdq_datepicker .ui-widget-icon-block {
    left: 50%;
    margin-left: -8px;
    display: block;
}

/* Misc visuals
----------------------------------*/

/* Overlays */
.nbdq_datepicker .ui-widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.nbdq_datepicker .ui-resizable {
    position: relative;
}
.nbdq_datepicker .ui-resizable-handle {
    position: absolute;
    font-size: 0.1px;
    display: block;
    -ms-touch-action: none;
    touch-action: none;
}
.nbdq_datepicker .ui-resizable-disabled .ui-resizable-handle,
.nbdq_datepicker .ui-resizable-autohide .ui-resizable-handle {
    display: none;
}
.nbdq_datepicker .ui-resizable-n {
    cursor: n-resize;
    height: 7px;
    width: 100%;
    top: -5px;
    left: 0;
}
.nbdq_datepicker .ui-resizable-s {
    cursor: s-resize;
    height: 7px;
    width: 100%;
    bottom: -5px;
    left: 0;
}
.nbdq_datepicker .ui-resizable-e {
    cursor: e-resize;
    width: 7px;
    right: -5px;
    top: 0;
    height: 100%;
}
.nbdq_datepicker .ui-resizable-w {
    cursor: w-resize;
    width: 7px;
    left: -5px;
    top: 0;
    height: 100%;
}
.nbdq_datepicker .ui-resizable-se {
    cursor: se-resize;
    width: 12px;
    height: 12px;
    right: 1px;
    bottom: 1px;
}
.nbdq_datepicker .ui-resizable-sw {
    cursor: sw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    bottom: -5px;
}
.nbdq_datepicker .ui-resizable-nw {
    cursor: nw-resize;
    width: 9px;
    height: 9px;
    left: -5px;
    top: -5px;
}
.nbdq_datepicker .ui-resizable-ne {
    cursor: ne-resize;
    width: 9px;
    height: 9px;
    right: -5px;
    top: -5px;
}
.nbdq_datepicker .ui-selectable {
    -ms-touch-action: none;
    touch-action: none;
}
.nbdq_datepicker .ui-selectable-helper {
    position: absolute;
    z-index: 100;
    border: 1px dotted black;
}
.nbdq_datepicker .ui-sortable-handle {
    -ms-touch-action: none;
    touch-action: none;
}
.nbdq_datepicker .ui-accordion .ui-accordion-header {
    display: block;
    cursor: pointer;
    position: relative;
    margin: 2px 0 0 0;
    padding: .5em .5em .5em .7em;
    font-size: 100%;
}
.nbdq_datepicker .ui-accordion .ui-accordion-content {
    padding: 1em 2.2em;
    border-top: 0;
    overflow: auto;
}
.nbdq_datepicker .ui-autocomplete {
    position: absolute;
    top: 0;
    left: 0;
    cursor: default;
}
.nbdq_datepicker .ui-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    display: block;
    outline: 0;
}
.nbdq_datepicker .ui-menu .ui-menu {
    position: absolute;
}
.nbdq_datepicker .ui-menu .ui-menu-item {
    margin: 0;
    cursor: pointer;
    /* support: IE10, see #8844 */
    list-style-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
}
.nbdq_datepicker .ui-menu .ui-menu-item-wrapper {
    position: relative;
    padding: 3px 1em 3px .4em;
}
.nbdq_datepicker .ui-menu .ui-menu-divider {
    margin: 5px 0;
    height: 0;
    font-size: 0;
    line-height: 0;
    border-width: 1px 0 0 0;
}
.nbdq_datepicker .ui-menu .ui-state-focus,
.nbdq_datepicker .ui-menu .ui-state-active {
    margin: -1px;
}

/* icon support */
.nbdq_datepicker .ui-menu-icons {
    position: relative;
}
.nbdq_datepicker .ui-menu-icons .ui-menu-item-wrapper {
    padding-left: 2em;
}

/* left-aligned */
.nbdq_datepicker .ui-menu .ui-icon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: .2em;
    margin: auto 0;
}

/* right-aligned */
.nbdq_datepicker .ui-menu .ui-menu-icon {
    left: auto;
    right: 0;
}
.nbdq_datepicker .ui-button {
    padding: .4em 1em;
    display: inline-block;
    position: relative;
    line-height: normal;
    margin-right: .1em;
    cursor: pointer;
    vertical-align: middle;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    /* Support: IE <= 11 */
    overflow: visible;
}

.nbdq_datepicker .ui-button,
.nbdq_datepicker .ui-button:link,
.nbdq_datepicker .ui-button:visited,
.nbdq_datepicker .ui-button:hover,
.nbdq_datepicker .ui-button:active {
    text-decoration: none;
}

/* to make room for the icon, a width needs to be set here */
.nbdq_datepicker .ui-button-icon-only {
    width: 2em;
    box-sizing: border-box;
    text-indent: -9999px;
    white-space: nowrap;
}

/* no icon support for input elements */
input.nbdq_datepicker .ui-button.ui-button-icon-only {
    text-indent: 0;
}

/* button icon element(s) */
.nbdq_datepicker .ui-button-icon-only .ui-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -8px;
    margin-left: -8px;
}

.nbdq_datepicker .ui-button.ui-icon-notext .ui-icon {
    padding: 0;
    width: 2.1em;
    height: 2.1em;
    text-indent: -9999px;
    white-space: nowrap;

}

input.nbdq_datepicker .ui-button.ui-icon-notext .ui-icon {
    width: auto;
    height: auto;
    text-indent: 0;
    white-space: normal;
    padding: .4em 1em;
}

/* workarounds */
/* Support: Firefox 5 - 40 */
input.nbdq_datepicker .ui-button::-moz-focus-inner,
button.nbdq_datepicker .ui-button::-moz-focus-inner {
    border: 0;
    padding: 0;
}
.nbdq_datepicker .ui-controlgroup {
    vertical-align: middle;
    display: inline-block;
}
.nbdq_datepicker .ui-controlgroup > .ui-controlgroup-item {
    float: left;
    margin-left: 0;
    margin-right: 0;
}
.nbdq_datepicker .ui-controlgroup > .ui-controlgroup-item:focus,
.nbdq_datepicker .ui-controlgroup > .ui-controlgroup-item.ui-visual-focus {
    z-index: 9999;
}
.nbdq_datepicker .ui-controlgroup-vertical > .ui-controlgroup-item {
    display: block;
    float: none;
    width: 100%;
    margin-top: 0;
    margin-bottom: 0;
    text-align: left;
}
.nbdq_datepicker .ui-controlgroup-vertical .ui-controlgroup-item {
    box-sizing: border-box;
}
.nbdq_datepicker .ui-controlgroup .ui-controlgroup-label {
    padding: .4em 1em;
}
.nbdq_datepicker .ui-controlgroup .ui-controlgroup-label span {
    font-size: 80%;
}
.nbdq_datepicker .ui-controlgroup-horizontal .ui-controlgroup-label + .ui-controlgroup-item {
    border-left: none;
}
.nbdq_datepicker .ui-controlgroup-vertical .ui-controlgroup-label + .ui-controlgroup-item {
    border-top: none;
}
.nbdq_datepicker .ui-controlgroup-horizontal .ui-controlgroup-label.ui-widget-content {
    border-right: none;
}
.nbdq_datepicker .ui-controlgroup-vertical .ui-controlgroup-label.ui-widget-content {
    border-bottom: none;
}

/* Spinner specific style fixes */
.nbdq_datepicker .ui-controlgroup-vertical .ui-spinner-input {

    /* Support: IE8 only, Android < 4.4 only */
    width: 75%;
    width: calc( 100% - 2.4em );
}
.nbdq_datepicker .ui-controlgroup-vertical .ui-spinner .ui-spinner-up {
    border-top-style: solid;
}

.nbdq_datepicker .ui-checkboxradio-label .ui-icon-background {
    box-shadow: inset 1px 1px 1px #ccc;
    border-radius: .12em;
    border: none;
}
.nbdq_datepicker .ui-checkboxradio-radio-label .ui-icon-background {
    width: 16px;
    height: 16px;
    border-radius: 1em;
    overflow: visible;
    border: none;
}
.nbdq_datepicker .ui-checkboxradio-radio-label.ui-checkboxradio-checked .ui-icon,
.nbdq_datepicker .ui-checkboxradio-radio-label.ui-checkboxradio-checked:hover .ui-icon {
    background-image: none;
    width: 8px;
    height: 8px;
    border-width: 4px;
    border-style: solid;
}
.nbdq_datepicker .ui-checkboxradio-disabled {
    pointer-events: none;
}
.nbdq_datepicker .ui-datepicker {
    width: 17em;
    padding: .2em .2em 0;
    display: none;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: .2em 0;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-prev,
.nbdq_datepicker .ui-datepicker .ui-datepicker-next {
    position: absolute;
    top: 2px;
    width: 1.8em;
    height: 1.8em;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-prev-hover,
.nbdq_datepicker .ui-datepicker .ui-datepicker-next-hover {
    top: 1px;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-prev {
    left: 2px;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-next {
    right: 2px;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-prev-hover {
    left: 1px;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-next-hover {
    right: 1px;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-prev span,
.nbdq_datepicker .ui-datepicker .ui-datepicker-next span {
    display: block;
    position: absolute;
    left: 50%;
    margin-left: -8px;
    top: 50%;
    margin-top: -8px;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-title {
    margin: 0 2.3em;
    line-height: 1.8em;
    text-align: center;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-title select {
    font-size: 1em;
    margin: 1px 0;
}
.nbdq_datepicker .ui-datepicker select.ui-datepicker-month,
.nbdq_datepicker .ui-datepicker select.ui-datepicker-year {
    width: 45%;
}
.nbdq_datepicker .ui-datepicker table {
    width: 100%;
    font-size: .9em;
    border-collapse: collapse;
    margin: 0 0 .4em;
}
.nbdq_datepicker .ui-datepicker th {
    padding: .7em .3em;
    text-align: center;
    font-weight: bold;
    border: 0;
}
.nbdq_datepicker .ui-datepicker td {
    border: 0;
    padding: 1px;
}
.nbdq_datepicker .ui-datepicker td span,
.nbdq_datepicker .ui-datepicker td a {
    display: block;
    padding: .2em;
    text-align: right;
    text-decoration: none;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-buttonpane {
    background-image: none;
    margin: .7em 0 0 0;
    padding: 0 .2em;
    border-left: 0;
    border-right: 0;
    border-bottom: 0;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-buttonpane button {
    float: right;
    margin: .5em .2em .4em;
    cursor: pointer;
    padding: .2em .6em .3em .6em;
    width: auto;
    overflow: visible;
}
.nbdq_datepicker .ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
    float: left;
}

/* with multiple calendars */
.nbdq_datepicker .ui-datepicker.ui-datepicker-multi {
    width: auto;
}
.nbdq_datepicker .ui-datepicker-multi .ui-datepicker-group {
    float: left;
}
.nbdq_datepicker .ui-datepicker-multi .ui-datepicker-group table {
    width: 95%;
    margin: 0 auto .4em;
}
.nbdq_datepicker .ui-datepicker-multi-2 .ui-datepicker-group {
    width: 50%;
}
.nbdq_datepicker .ui-datepicker-multi-3 .ui-datepicker-group {
    width: 33.3%;
}
.nbdq_datepicker .ui-datepicker-multi-4 .ui-datepicker-group {
    width: 25%;
}
.nbdq_datepicker .ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.nbdq_datepicker .ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
    border-left-width: 0;
}
.nbdq_datepicker .ui-datepicker-multi .ui-datepicker-buttonpane {
    clear: left;
}
.nbdq_datepicker .ui-datepicker-row-break {
    clear: both;
    width: 100%;
    font-size: 0;
}

/* RTL support */
.nbdq_datepicker .ui-datepicker-rtl {
    direction: rtl;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-prev {
    right: 2px;
    left: auto;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-next {
    left: 2px;
    right: auto;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-prev:hover {
    right: 1px;
    left: auto;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-next:hover {
    left: 1px;
    right: auto;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-buttonpane {
    clear: right;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-buttonpane button {
    float: left;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-group {
    float: right;
}
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,
.nbdq_datepicker .ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
    border-right-width: 0;
    border-left-width: 1px;
}

/* Icons */
.nbdq_datepicker .ui-datepicker .ui-icon {
    display: block;
    text-indent: -99999px;
    overflow: hidden;
    background-repeat: no-repeat;
    left: .5em;
    top: .3em;
}
.nbdq_datepicker .ui-dialog {
    position: absolute;
    top: 0;
    left: 0;
    padding: .2em;
    outline: 0;
}
.nbdq_datepicker .ui-dialog .ui-dialog-titlebar {
    padding: .4em 1em;
    position: relative;
}
.nbdq_datepicker .ui-dialog .ui-dialog-title {
    float: left;
    margin: .1em 0;
    white-space: nowrap;
    width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.nbdq_datepicker .ui-dialog .ui-dialog-titlebar-close {
    position: absolute;
    right: .3em;
    top: 50%;
    width: 20px;
    margin: -10px 0 0 0;
    padding: 1px;
    height: 20px;
}
.nbdq_datepicker .ui-dialog .ui-dialog-content {
    position: relative;
    border: 0;
    padding: .5em 1em;
    background: none;
    overflow: auto;
}
.nbdq_datepicker .ui-dialog .ui-dialog-buttonpane {
    text-align: left;
    border-width: 1px 0 0 0;
    background-image: none;
    margin-top: .5em;
    padding: .3em 1em .5em .4em;
}
.nbdq_datepicker .ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
    float: right;
}
.nbdq_datepicker .ui-dialog .ui-dialog-buttonpane button {
    margin: .5em .4em .5em 0;
    cursor: pointer;
}
.nbdq_datepicker .ui-dialog .ui-resizable-n {
    height: 2px;
    top: 0;
}
.nbdq_datepicker .ui-dialog .ui-resizable-e {
    width: 2px;
    right: 0;
}
.nbdq_datepicker .ui-dialog .ui-resizable-s {
    height: 2px;
    bottom: 0;
}
.nbdq_datepicker .ui-dialog .ui-resizable-w {
    width: 2px;
    left: 0;
}
.nbdq_datepicker .ui-dialog .ui-resizable-se,
.nbdq_datepicker .ui-dialog .ui-resizable-sw,
.nbdq_datepicker .ui-dialog .ui-resizable-ne,
.nbdq_datepicker .ui-dialog .ui-resizable-nw {
    width: 7px;
    height: 7px;
}
.nbdq_datepicker .ui-dialog .ui-resizable-se {
    right: 0;
    bottom: 0;
}
.nbdq_datepicker .ui-dialog .ui-resizable-sw {
    left: 0;
    bottom: 0;
}
.nbdq_datepicker .ui-dialog .ui-resizable-ne {
    right: 0;
    top: 0;
}
.nbdq_datepicker .ui-dialog .ui-resizable-nw {
    left: 0;
    top: 0;
}
.nbdq_datepicker .ui-draggable .ui-dialog-titlebar {
    cursor: move;
}
.nbdq_datepicker .ui-progressbar {
    height: 2em;
    text-align: left;
    overflow: hidden;
}
.nbdq_datepicker .ui-progressbar .ui-progressbar-value {
    margin: -1px;
    height: 100%;
}
.nbdq_datepicker .ui-progressbar .ui-progressbar-overlay {
    background: url("data:image/gif;base64,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");
    height: 100%;
    filter: alpha(opacity=25); /* support: IE8 */
    opacity: 0.25;
}
.nbdq_datepicker .ui-progressbar-indeterminate .ui-progressbar-value {
    background-image: none;
}
.nbdq_datepicker .ui-selectmenu-menu {
    padding: 0;
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    display: none;
}
.nbdq_datepicker .ui-selectmenu-menu .ui-menu {
    overflow: auto;
    overflow-x: hidden;
    padding-bottom: 1px;
}
.nbdq_datepicker .ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
    font-size: 1em;
    font-weight: bold;
    line-height: 1.5;
    padding: 2px 0.4em;
    margin: 0.5em 0 0 0;
    height: auto;
    border: 0;
}
.nbdq_datepicker .ui-selectmenu-open {
    display: block;
}
.nbdq_datepicker .ui-selectmenu-text {
    display: block;
    margin-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
}
.nbdq_datepicker .ui-selectmenu-button.ui-button {
    text-align: left;
    white-space: nowrap;
    width: 14em;
}
.nbdq_datepicker .ui-selectmenu-icon.ui-icon {
    float: right;
    margin-top: 0;
}
.nbdq_datepicker .ui-slider {
    position: relative;
    text-align: left;
}
.nbdq_datepicker .ui-slider .ui-slider-handle {
    position: absolute;
    z-index: 2;
    width: 1.2em;
    height: 1.2em;
    cursor: default;
    -ms-touch-action: none;
    touch-action: none;
}
.nbdq_datepicker .ui-slider .ui-slider-range {
    position: absolute;
    z-index: 1;
    font-size: .7em;
    display: block;
    border: 0;
    background-position: 0 0;
}

/* support: IE8 - See #6727 */
.nbdq_datepicker .ui-slider.ui-state-disabled .ui-slider-handle,
.nbdq_datepicker .ui-slider.ui-state-disabled .ui-slider-range {
    filter: inherit;
}

.nbdq_datepicker .ui-slider-horizontal {
    height: .8em;
}
.nbdq_datepicker .ui-slider-horizontal .ui-slider-handle {
    top: -.3em;
    margin-left: -.6em;
}
.nbdq_datepicker .ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%;
}
.nbdq_datepicker .ui-slider-horizontal .ui-slider-range-min {
    left: 0;
}
.nbdq_datepicker .ui-slider-horizontal .ui-slider-range-max {
    right: 0;
}

.nbdq_datepicker .ui-slider-vertical {
    width: .8em;
    height: 100px;
}
.nbdq_datepicker .ui-slider-vertical .ui-slider-handle {
    left: -.3em;
    margin-left: 0;
    margin-bottom: -.6em;
}
.nbdq_datepicker .ui-slider-vertical .ui-slider-range {
    left: 0;
    width: 100%;
}
.nbdq_datepicker .ui-slider-vertical .ui-slider-range-min {
    bottom: 0;
}
.nbdq_datepicker .ui-slider-vertical .ui-slider-range-max {
    top: 0;
}
.nbdq_datepicker .ui-spinner {
    position: relative;
    display: inline-block;
    overflow: hidden;
    padding: 0;
    vertical-align: middle;
}
.nbdq_datepicker .ui-spinner-input {
    border: none;
    background: none;
    color: inherit;
    padding: .222em 0;
    margin: .2em 0;
    vertical-align: middle;
    margin-left: .4em;
    margin-right: 2em;
}
.nbdq_datepicker .ui-spinner-button {
    width: 1.6em;
    height: 50%;
    font-size: .5em;
    padding: 0;
    margin: 0;
    text-align: center;
    position: absolute;
    cursor: default;
    display: block;
    overflow: hidden;
    right: 0;
}
/* more specificity required here to override default borders */
.nbdq_datepicker .ui-spinner a.ui-spinner-button {
    border-top-style: none;
    border-bottom-style: none;
    border-right-style: none;
}
.nbdq_datepicker .ui-spinner-up {
    top: 0;
}
.nbdq_datepicker .ui-spinner-down {
    bottom: 0;
}
.nbdq_datepicker .ui-tabs {
    position: relative;/* position: relative prevents IE scroll bug (element with position: relative inside container with overflow: auto appear as "fixed") */
    padding: .2em;
}
.nbdq_datepicker .ui-tabs .ui-tabs-nav {
    margin: 0;
    padding: .2em .2em 0;
}
.nbdq_datepicker .ui-tabs .ui-tabs-nav li {
    list-style: none;
    float: left;
    position: relative;
    top: 0;
    margin: 1px .2em 0 0;
    border-bottom-width: 0;
    padding: 0;
    white-space: nowrap;
}
.nbdq_datepicker .ui-tabs .ui-tabs-nav .ui-tabs-anchor {
    float: left;
    padding: .5em 1em;
    text-decoration: none;
}
.nbdq_datepicker .ui-tabs .ui-tabs-nav li.ui-tabs-active {
    margin-bottom: -1px;
    padding-bottom: 1px;
}
.nbdq_datepicker .ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor,
.nbdq_datepicker .ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor,
.nbdq_datepicker .ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
    cursor: text;
}
.nbdq_datepicker .ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor {
    cursor: pointer;
}
.nbdq_datepicker .ui-tabs .ui-tabs-panel {
    display: block;
    border-width: 0;
    padding: 1em 1.4em;
    background: none;
}
.nbdq_datepicker .ui-tooltip {
    padding: 8px;
    position: absolute;
    z-index: 9999;
    max-width: 300px;
}
body .nbdq_datepicker .ui-tooltip {
    border-width: 2px;
}

/* Component containers
----------------------------------*/
.nbdq_datepicker .ui-widget {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
}
.nbdq_datepicker .ui-widget .ui-widget {
    font-size: 1em;
}
.nbdq_datepicker .ui-widget input,
.nbdq_datepicker .ui-widget select,
.nbdq_datepicker .ui-widget textarea,
.nbdq_datepicker .ui-widget button {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
}
.nbdq_datepicker .ui-widget.ui-widget-content {
    border: 1px solid #c5c5c5;
}
.nbdq_datepicker .ui-widget-content {
    border: 1px solid #dddddd;
    background: #ffffff;
    color: #333333;
}
.nbdq_datepicker .ui-widget-content a {
    color: #333333;
}
.nbdq_datepicker .ui-widget-header {
    border: 1px solid #dddddd;
    background: #e9e9e9;
    color: #333333;
    font-weight: bold;
}
.nbdq_datepicker .ui-widget-header a {
    color: #333333;
}

/* Interaction states
----------------------------------*/
.nbdq_datepicker .ui-state-default,
.nbdq_datepicker .ui-widget-content .ui-state-default,
.nbdq_datepicker .ui-widget-header .ui-state-default,
.nbdq_datepicker .ui-button,

/* We use html here because we need a greater specificity to make sure disabled
works properly when clicked or hovered */
html .nbdq_datepicker .ui-button.ui-state-disabled:hover,
html .nbdq_datepicker .ui-button.ui-state-disabled:active {
    border: 1px solid #c5c5c5;
    background: #f6f6f6;
    font-weight: normal;
    color: #454545;
}
.nbdq_datepicker .ui-state-default a,
.nbdq_datepicker .ui-state-default a:link,
.nbdq_datepicker .ui-state-default a:visited,
a.nbdq_datepicker .ui-button,
a:link.nbdq_datepicker .ui-button,
a:visited.nbdq_datepicker .ui-button,
.nbdq_datepicker .ui-button {
    color: #454545;
    text-decoration: none;
}
.nbdq_datepicker .ui-state-hover,
.nbdq_datepicker .ui-widget-content .ui-state-hover,
.nbdq_datepicker .ui-widget-header .ui-state-hover,
.nbdq_datepicker .ui-state-focus,
.nbdq_datepicker .ui-widget-content .ui-state-focus,
.nbdq_datepicker .ui-widget-header .ui-state-focus,
.nbdq_datepicker .ui-button:hover,
.nbdq_datepicker .ui-button:focus {
    border: 1px solid #cccccc;
    background: #ededed;
    font-weight: normal;
    color: #2b2b2b;
}
.nbdq_datepicker .ui-state-hover a,
.nbdq_datepicker .ui-state-hover a:hover,
.nbdq_datepicker .ui-state-hover a:link,
.nbdq_datepicker .ui-state-hover a:visited,
.nbdq_datepicker .ui-state-focus a,
.nbdq_datepicker .ui-state-focus a:hover,
.nbdq_datepicker .ui-state-focus a:link,
.nbdq_datepicker .ui-state-focus a:visited,
a.nbdq_datepicker .ui-button:hover,
a.nbdq_datepicker .ui-button:focus {
    color: #2b2b2b;
    text-decoration: none;
}

.nbdq_datepicker .ui-visual-focus {
    box-shadow: 0 0 3px 1px rgb(94, 158, 214);
}
.nbdq_datepicker .ui-state-active,
.nbdq_datepicker .ui-widget-content .ui-state-active,
.nbdq_datepicker .ui-widget-header .ui-state-active,
a.nbdq_datepicker .ui-button:active,
.nbdq_datepicker .ui-button:active,
.nbdq_datepicker .ui-button.ui-state-active:hover {
    border: 1px solid #003eff;
    background: #007fff;
    font-weight: normal;
    color: #ffffff;
}
.nbdq_datepicker .ui-icon-background,
.nbdq_datepicker .ui-state-active .ui-icon-background {
    border: #003eff;
    background-color: #ffffff;
}
.nbdq_datepicker .ui-state-active a,
.nbdq_datepicker .ui-state-active a:link,
.nbdq_datepicker .ui-state-active a:visited {
    color: #ffffff;
    text-decoration: none;
}

/* Interaction Cues
----------------------------------*/
.nbdq_datepicker .ui-state-highlight,
.nbdq_datepicker .ui-widget-content .ui-state-highlight,
.nbdq_datepicker .ui-widget-header .ui-state-highlight {
    border: 1px solid #dad55e;
    background: #fffa90;
    color: #777620;
}
.nbdq_datepicker .ui-state-checked {
    border: 1px solid #dad55e;
    background: #fffa90;
}
.nbdq_datepicker .ui-state-highlight a,
.nbdq_datepicker .ui-widget-content .ui-state-highlight a,
.nbdq_datepicker .ui-widget-header .ui-state-highlight a {
    color: #777620;
}
.nbdq_datepicker .ui-state-error,
.nbdq_datepicker .ui-widget-content .ui-state-error,
.nbdq_datepicker .ui-widget-header .ui-state-error {
    border: 1px solid #f1a899;
    background: #fddfdf;
    color: #5f3f3f;
}
.nbdq_datepicker .ui-state-error a,
.nbdq_datepicker .ui-widget-content .ui-state-error a,
.nbdq_datepicker .ui-widget-header .ui-state-error a {
    color: #5f3f3f;
}
.nbdq_datepicker .ui-state-error-text,
.nbdq_datepicker .ui-widget-content .ui-state-error-text,
.nbdq_datepicker .ui-widget-header .ui-state-error-text {
    color: #5f3f3f;
}
.nbdq_datepicker .ui-priority-primary,
.nbdq_datepicker .ui-widget-content .ui-priority-primary,
.nbdq_datepicker .ui-widget-header .ui-priority-primary {
    font-weight: bold;
}
.nbdq_datepicker .ui-priority-secondary,
.nbdq_datepicker .ui-widget-content .ui-priority-secondary,
.nbdq_datepicker .ui-widget-header .ui-priority-secondary {
    opacity: .7;
    filter:Alpha(Opacity=70); /* support: IE8 */
    font-weight: normal;
}
.nbdq_datepicker .ui-state-disabled,
.nbdq_datepicker .ui-widget-content .ui-state-disabled,
.nbdq_datepicker .ui-widget-header .ui-state-disabled {
    opacity: .35;
    filter:Alpha(Opacity=35); /* support: IE8 */
    background-image: none;
}
.nbdq_datepicker .ui-state-disabled .ui-icon {
    filter:Alpha(Opacity=35); /* support: IE8 - See #6059 */
}

/* Icons
----------------------------------*/

/* states and images */
.nbdq_datepicker .ui-icon {
    width: 16px;
    height: 16px;
}
.nbdq_datepicker .ui-icon,
.nbdq_datepicker .ui-widget-content .ui-icon {
    background-image: url("images/ui-icons_444444_256x240.png");
}
.nbdq_datepicker .ui-widget-header .ui-icon {
    background-image: url("images/ui-icons_444444_256x240.png");
}
.nbdq_datepicker .ui-state-hover .ui-icon,
.nbdq_datepicker .ui-state-focus .ui-icon,
.nbdq_datepicker .ui-button:hover .ui-icon,
.nbdq_datepicker .ui-button:focus .ui-icon {
    background-image: url("images/ui-icons_555555_256x240.png");
}
.nbdq_datepicker .ui-state-active .ui-icon,
.nbdq_datepicker .ui-button:active .ui-icon {
    background-image: url("images/ui-icons_ffffff_256x240.png");
}
.nbdq_datepicker .ui-state-highlight .ui-icon,
.nbdq_datepicker .ui-button .ui-state-highlight.ui-icon {
    background-image: url("images/ui-icons_777620_256x240.png");
}
.nbdq_datepicker .ui-state-error .ui-icon,
.nbdq_datepicker .ui-state-error-text .ui-icon {
    background-image: url("images/ui-icons_cc0000_256x240.png");
}
.nbdq_datepicker .ui-button .ui-icon {
    background-image: url("images/ui-icons_777777_256x240.png");
}

/* positioning */
.nbdq_datepicker .ui-icon-blank { background-position: 16px 16px; }
.nbdq_datepicker .ui-icon-caret-1-n { background-position: 0 0; }
.nbdq_datepicker .ui-icon-caret-1-ne { background-position: -16px 0; }
.nbdq_datepicker .ui-icon-caret-1-e { background-position: -32px 0; }
.nbdq_datepicker .ui-icon-caret-1-se { background-position: -48px 0; }
.nbdq_datepicker .ui-icon-caret-1-s { background-position: -65px 0; }
.nbdq_datepicker .ui-icon-caret-1-sw { background-position: -80px 0; }
.nbdq_datepicker .ui-icon-caret-1-w { background-position: -96px 0; }
.nbdq_datepicker .ui-icon-caret-1-nw { background-position: -112px 0; }
.nbdq_datepicker .ui-icon-caret-2-n-s { background-position: -128px 0; }
.nbdq_datepicker .ui-icon-caret-2-e-w { background-position: -144px 0; }
.nbdq_datepicker .ui-icon-triangle-1-n { background-position: 0 -16px; }
.nbdq_datepicker .ui-icon-triangle-1-ne { background-position: -16px -16px; }
.nbdq_datepicker .ui-icon-triangle-1-e { background-position: -32px -16px; }
.nbdq_datepicker .ui-icon-triangle-1-se { background-position: -48px -16px; }
.nbdq_datepicker .ui-icon-triangle-1-s { background-position: -65px -16px; }
.nbdq_datepicker .ui-icon-triangle-1-sw { background-position: -80px -16px; }
.nbdq_datepicker .ui-icon-triangle-1-w { background-position: -96px -16px; }
.nbdq_datepicker .ui-icon-triangle-1-nw { background-position: -112px -16px; }
.nbdq_datepicker .ui-icon-triangle-2-n-s { background-position: -128px -16px; }
.nbdq_datepicker .ui-icon-triangle-2-e-w { background-position: -144px -16px; }
.nbdq_datepicker .ui-icon-arrow-1-n { background-position: 0 -32px; }
.nbdq_datepicker .ui-icon-arrow-1-ne { background-position: -16px -32px; }
.nbdq_datepicker .ui-icon-arrow-1-e { background-position: -32px -32px; }
.nbdq_datepicker .ui-icon-arrow-1-se { background-position: -48px -32px; }
.nbdq_datepicker .ui-icon-arrow-1-s { background-position: -65px -32px; }
.nbdq_datepicker .ui-icon-arrow-1-sw { background-position: -80px -32px; }
.nbdq_datepicker .ui-icon-arrow-1-w { background-position: -96px -32px; }
.nbdq_datepicker .ui-icon-arrow-1-nw { background-position: -112px -32px; }
.nbdq_datepicker .ui-icon-arrow-2-n-s { background-position: -128px -32px; }
.nbdq_datepicker .ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }
.nbdq_datepicker .ui-icon-arrow-2-e-w { background-position: -160px -32px; }
.nbdq_datepicker .ui-icon-arrow-2-se-nw { background-position: -176px -32px; }
.nbdq_datepicker .ui-icon-arrowstop-1-n { background-position: -192px -32px; }
.nbdq_datepicker .ui-icon-arrowstop-1-e { background-position: -208px -32px; }
.nbdq_datepicker .ui-icon-arrowstop-1-s { background-position: -224px -32px; }
.nbdq_datepicker .ui-icon-arrowstop-1-w { background-position: -240px -32px; }
.nbdq_datepicker .ui-icon-arrowthick-1-n { background-position: 1px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-1-ne { background-position: -16px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-1-e { background-position: -32px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-1-se { background-position: -48px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-1-s { background-position: -64px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-1-sw { background-position: -80px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-1-w { background-position: -96px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-1-nw { background-position: -112px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }
.nbdq_datepicker .ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }
.nbdq_datepicker .ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }
.nbdq_datepicker .ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }
.nbdq_datepicker .ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }
.nbdq_datepicker .ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }
.nbdq_datepicker .ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }
.nbdq_datepicker .ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }
.nbdq_datepicker .ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }
.nbdq_datepicker .ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }
.nbdq_datepicker .ui-icon-arrowreturn-1-w { background-position: -64px -64px; }
.nbdq_datepicker .ui-icon-arrowreturn-1-n { background-position: -80px -64px; }
.nbdq_datepicker .ui-icon-arrowreturn-1-e { background-position: -96px -64px; }
.nbdq_datepicker .ui-icon-arrowreturn-1-s { background-position: -112px -64px; }
.nbdq_datepicker .ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }
.nbdq_datepicker .ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }
.nbdq_datepicker .ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }
.nbdq_datepicker .ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }
.nbdq_datepicker .ui-icon-arrow-4 { background-position: 0 -80px; }
.nbdq_datepicker .ui-icon-arrow-4-diag { background-position: -16px -80px; }
.nbdq_datepicker .ui-icon-extlink { background-position: -32px -80px; }
.nbdq_datepicker .ui-icon-newwin { background-position: -48px -80px; }
.nbdq_datepicker .ui-icon-refresh { background-position: -64px -80px; }
.nbdq_datepicker .ui-icon-shuffle { background-position: -80px -80px; }
.nbdq_datepicker .ui-icon-transfer-e-w { background-position: -96px -80px; }
.nbdq_datepicker .ui-icon-transferthick-e-w { background-position: -112px -80px; }
.nbdq_datepicker .ui-icon-folder-collapsed { background-position: 0 -96px; }
.nbdq_datepicker .ui-icon-folder-open { background-position: -16px -96px; }
.nbdq_datepicker .ui-icon-document { background-position: -32px -96px; }
.nbdq_datepicker .ui-icon-document-b { background-position: -48px -96px; }
.nbdq_datepicker .ui-icon-note { background-position: -64px -96px; }
.nbdq_datepicker .ui-icon-mail-closed { background-position: -80px -96px; }
.nbdq_datepicker .ui-icon-mail-open { background-position: -96px -96px; }
.nbdq_datepicker .ui-icon-suitcase { background-position: -112px -96px; }
.nbdq_datepicker .ui-icon-comment { background-position: -128px -96px; }
.nbdq_datepicker .ui-icon-person { background-position: -144px -96px; }
.nbdq_datepicker .ui-icon-print { background-position: -160px -96px; }
.nbdq_datepicker .ui-icon-trash { background-position: -176px -96px; }
.nbdq_datepicker .ui-icon-locked { background-position: -192px -96px; }
.nbdq_datepicker .ui-icon-unlocked { background-position: -208px -96px; }
.nbdq_datepicker .ui-icon-bookmark { background-position: -224px -96px; }
.nbdq_datepicker .ui-icon-tag { background-position: -240px -96px; }
.nbdq_datepicker .ui-icon-home { background-position: 0 -112px; }
.nbdq_datepicker .ui-icon-flag { background-position: -16px -112px; }
.nbdq_datepicker .ui-icon-calendar { background-position: -32px -112px; }
.nbdq_datepicker .ui-icon-cart { background-position: -48px -112px; }
.nbdq_datepicker .ui-icon-pencil { background-position: -64px -112px; }
.nbdq_datepicker .ui-icon-clock { background-position: -80px -112px; }
.nbdq_datepicker .ui-icon-disk { background-position: -96px -112px; }
.nbdq_datepicker .ui-icon-calculator { background-position: -112px -112px; }
.nbdq_datepicker .ui-icon-zoomin { background-position: -128px -112px; }
.nbdq_datepicker .ui-icon-zoomout { background-position: -144px -112px; }
.nbdq_datepicker .ui-icon-search { background-position: -160px -112px; }
.nbdq_datepicker .ui-icon-wrench { background-position: -176px -112px; }
.nbdq_datepicker .ui-icon-gear { background-position: -192px -112px; }
.nbdq_datepicker .ui-icon-heart { background-position: -208px -112px; }
.nbdq_datepicker .ui-icon-star { background-position: -224px -112px; }
.nbdq_datepicker .ui-icon-link { background-position: -240px -112px; }
.nbdq_datepicker .ui-icon-cancel { background-position: 0 -128px; }
.nbdq_datepicker .ui-icon-plus { background-position: -16px -128px; }
.nbdq_datepicker .ui-icon-plusthick { background-position: -32px -128px; }
.nbdq_datepicker .ui-icon-minus { background-position: -48px -128px; }
.nbdq_datepicker .ui-icon-minusthick { background-position: -64px -128px; }
.nbdq_datepicker .ui-icon-close { background-position: -80px -128px; }
.nbdq_datepicker .ui-icon-closethick { background-position: -96px -128px; }
.nbdq_datepicker .ui-icon-key { background-position: -112px -128px; }
.nbdq_datepicker .ui-icon-lightbulb { background-position: -128px -128px; }
.nbdq_datepicker .ui-icon-scissors { background-position: -144px -128px; }
.nbdq_datepicker .ui-icon-clipboard { background-position: -160px -128px; }
.nbdq_datepicker .ui-icon-copy { background-position: -176px -128px; }
.nbdq_datepicker .ui-icon-contact { background-position: -192px -128px; }
.nbdq_datepicker .ui-icon-image { background-position: -208px -128px; }
.nbdq_datepicker .ui-icon-video { background-position: -224px -128px; }
.nbdq_datepicker .ui-icon-script { background-position: -240px -128px; }
.nbdq_datepicker .ui-icon-alert { background-position: 0 -144px; }
.nbdq_datepicker .ui-icon-info { background-position: -16px -144px; }
.nbdq_datepicker .ui-icon-notice { background-position: -32px -144px; }
.nbdq_datepicker .ui-icon-help { background-position: -48px -144px; }
.nbdq_datepicker .ui-icon-check { background-position: -64px -144px; }
.nbdq_datepicker .ui-icon-bullet { background-position: -80px -144px; }
.nbdq_datepicker .ui-icon-radio-on { background-position: -96px -144px; }
.nbdq_datepicker .ui-icon-radio-off { background-position: -112px -144px; }
.nbdq_datepicker .ui-icon-pin-w { background-position: -128px -144px; }
.nbdq_datepicker .ui-icon-pin-s { background-position: -144px -144px; }
.nbdq_datepicker .ui-icon-play { background-position: 0 -160px; }
.nbdq_datepicker .ui-icon-pause { background-position: -16px -160px; }
.nbdq_datepicker .ui-icon-seek-next { background-position: -32px -160px; }
.nbdq_datepicker .ui-icon-seek-prev { background-position: -48px -160px; }
.nbdq_datepicker .ui-icon-seek-end { background-position: -64px -160px; }
.nbdq_datepicker .ui-icon-seek-start { background-position: -80px -160px; }
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.nbdq_datepicker .ui-icon-seek-first { background-position: -80px -160px; }
.nbdq_datepicker .ui-icon-stop { background-position: -96px -160px; }
.nbdq_datepicker .ui-icon-eject { background-position: -112px -160px; }
.nbdq_datepicker .ui-icon-volume-off { background-position: -128px -160px; }
.nbdq_datepicker .ui-icon-volume-on { background-position: -144px -160px; }
.nbdq_datepicker .ui-icon-power { background-position: 0 -176px; }
.nbdq_datepicker .ui-icon-signal-diag { background-position: -16px -176px; }
.nbdq_datepicker .ui-icon-signal { background-position: -32px -176px; }
.nbdq_datepicker .ui-icon-battery-0 { background-position: -48px -176px; }
.nbdq_datepicker .ui-icon-battery-1 { background-position: -64px -176px; }
.nbdq_datepicker .ui-icon-battery-2 { background-position: -80px -176px; }
.nbdq_datepicker .ui-icon-battery-3 { background-position: -96px -176px; }
.nbdq_datepicker .ui-icon-circle-plus { background-position: 0 -192px; }
.nbdq_datepicker .ui-icon-circle-minus { background-position: -16px -192px; }
.nbdq_datepicker .ui-icon-circle-close { background-position: -32px -192px; }
.nbdq_datepicker .ui-icon-circle-triangle-e { background-position: -48px -192px; }
.nbdq_datepicker .ui-icon-circle-triangle-s { background-position: -64px -192px; }
.nbdq_datepicker .ui-icon-circle-triangle-w { background-position: -80px -192px; }
.nbdq_datepicker .ui-icon-circle-triangle-n { background-position: -96px -192px; }
.nbdq_datepicker .ui-icon-circle-arrow-e { background-position: -112px -192px; }
.nbdq_datepicker .ui-icon-circle-arrow-s { background-position: -128px -192px; }
.nbdq_datepicker .ui-icon-circle-arrow-w { background-position: -144px -192px; }
.nbdq_datepicker .ui-icon-circle-arrow-n { background-position: -160px -192px; }
.nbdq_datepicker .ui-icon-circle-zoomin { background-position: -176px -192px; }
.nbdq_datepicker .ui-icon-circle-zoomout { background-position: -192px -192px; }
.nbdq_datepicker .ui-icon-circle-check { background-position: -208px -192px; }
.nbdq_datepicker .ui-icon-circlesmall-plus { background-position: 0 -208px; }
.nbdq_datepicker .ui-icon-circlesmall-minus { background-position: -16px -208px; }
.nbdq_datepicker .ui-icon-circlesmall-close { background-position: -32px -208px; }
.nbdq_datepicker .ui-icon-squaresmall-plus { background-position: -48px -208px; }
.nbdq_datepicker .ui-icon-squaresmall-minus { background-position: -64px -208px; }
.nbdq_datepicker .ui-icon-squaresmall-close { background-position: -80px -208px; }
.nbdq_datepicker .ui-icon-grip-dotted-vertical { background-position: 0 -224px; }
.nbdq_datepicker .ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }
.nbdq_datepicker .ui-icon-grip-solid-vertical { background-position: -32px -224px; }
.nbdq_datepicker .ui-icon-grip-solid-horizontal { background-position: -48px -224px; }
.nbdq_datepicker .ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }
.nbdq_datepicker .ui-icon-grip-diagonal-se { background-position: -80px -224px; }


/* Misc visuals
----------------------------------*/

/* Corner radius */
.nbdq_datepicker .ui-corner-all,
.nbdq_datepicker .ui-corner-top,
.nbdq_datepicker .ui-corner-left,
.nbdq_datepicker .ui-corner-tl {
    border-top-left-radius: 3px;
}
.nbdq_datepicker .ui-corner-all,
.nbdq_datepicker .ui-corner-top,
.nbdq_datepicker .ui-corner-right,
.nbdq_datepicker .ui-corner-tr {
    border-top-right-radius: 3px;
}
.nbdq_datepicker .ui-corner-all,
.nbdq_datepicker .ui-corner-bottom,
.nbdq_datepicker .ui-corner-left,
.nbdq_datepicker .ui-corner-bl {
    border-bottom-left-radius: 3px;
}
.nbdq_datepicker .ui-corner-all,
.nbdq_datepicker .ui-corner-bottom,
.nbdq_datepicker .ui-corner-right,
.nbdq_datepicker .ui-corner-br {
    border-bottom-right-radius: 3px;
}

/* Overlays */
.nbdq_datepicker .ui-widget-overlay {
    background: #aaaaaa;
    opacity: .3;
    filter: Alpha(Opacity=30); /* support: IE8 */
}
.nbdq_datepicker .ui-widget-shadow {
    -webkit-box-shadow: 0px 0px 5px #666666;
    box-shadow: 0px 0px 5px #666666;
}
p.form-row .ti_tx,
p.form-row .mi_tx,
p.form-row .mer_tx {
    width: 100%;
    text-align: center;
    margin: 5px 0;
}
p.form-row .time,
p.form-row .mins,
p.form-row .meridian {
    width: 60px;
    margin: 0;
    font-size: 20px;
    color: #2d2e2e;
    font-family: arial;
    font-weight: 700;
    display: inline-block;
}
p.form-row .prev,
p.form-row .next {
    cursor: pointer;
    padding: 15px;
    width: 28%;
    border: 1px solid #ccc;
    margin: auto;
    background: url(../images/arrow2.png) no-repeat;
    border-radius: 5px;
    background-size: 60%;
}
p.form-row .prev:hover,
p.form-row .next:hover {
    background-color: #ccc;
}
p.form-row .next {
    background-position: 50% 140%;
}
p.form-row .prev {
    background-position: 50% -50%;
}
p.form-row .time_pick {
    position: relative;
}
p.form-row .timepicker_wrap {
    padding: 10px;
    border-radius: 5px;
    z-index: 999998;
    display: none;
    box-shadow: 2px 2px 5px 0 rgba(50,50,50,0.35);
    background: #f6f6f6;
    border: 1px solid #ccc;
    float: left;
    position: absolute;
    top: 27px;
    left: 0;
}
p.form-row .arrow_top {
    position: absolute;
    top: -10px;
    left: 20px;
    background: url(../images/top_arr.png) no-repeat;
    width: 18px;
    height: 10px;
    z-index: 999;
}
p.form-row input.timepicki-input {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #CCCCCC;
    border-radius: 5px 2px;
    font-size: 16px;
    float: none;
    margin: 0;
    text-align: center;
    width: 70%;
}
p.form-row a.reset_time {
    float: left;
    margin-top: 5px;
    color: #000;
}

.nbdq-popup {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: -1;
    opacity: 0;
    visibility: hidden;
    transition: all .3s; 
    overflow: hidden;
    overflow-y: auto;
}
.nbdq-popup .main-popup {
    position: absolute;
    pointer-events: all;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 0 42px rgba(0, 0, 0, 0.15);
    box-sizing: border-box;
    text-align: left;
    width: 525px;
    transition: all .6s;
}
.nbdq-popup .main-popup .nbdq-popup-body{
    padding: 20px;
}
.nbdq-popup .main-popup h3.nbdq-head {
    padding: 10px 20px;
    margin: 0;
    border-bottom: 1px solid #ddd;
}
.nbdq-popup.nb-show {
    opacity: 1;
    visibility: visible;
    z-index: 9999999; 
}
.nbdq-popup[data-animate="scale"] .main-popup {
    transform: scale(0.8);
    transition: all .3s; }
.nbdq-popup[data-animate="scale"].nb-show .main-popup {
    transform: scale(1); }
.nbdq-popup[data-animate="bottom-to-top"] .main-popup {
    transform: translate(0, 50%);
    transition: all .3s; }
.nbdq-popup[data-animate="bottom-to-top"].nb-show .main-popup {
    transform: translate(0, 0); }
.nbdq-popup[data-animate="top-to-bottom"] .main-popup {
    transform: translate(0, -50%);
    transition: all .3s; }
.nbdq-popup[data-animate="top-to-bottom"].nb-show .main-popup {
    transform: translate(0, 0); }
.nbdq-popup[data-animate="left-to-right"] .main-popup {
    transform: translate(-50%, 0);
    transition: all .3s; }
.nbdq-popup[data-animate="left-to-right"].nb-show .main-popup {
    transform: translate(0, 0); }
.nbdq-popup[data-animate="right-to-left"] .main-popup {
    transform: translate(50%, 0);
    transition: all .3s; }
.nbdq-popup[data-animate="right-to-left"].nb-show .main-popup {
    transform: translate(0, 0); }
.nbdq-popup[data-animate="fixed-top"] {
    align-items: flex-start; }
.nbdq-popup[data-animate="fixed-top"] .main-popup {
    margin-top: 60px;
    transform: translate(0, -50%);
    transition: all .3s; }
.nbdq-popup[data-animate="fixed-top"].nb-show .main-popup {
    transform: translate(0, 0); 
}
.nbdq-popup .close-popup {
    position: absolute;
    top: -15px;
    right: -15px;
    display: block;
    width: 30px;
    height: 30px;
    padding: 3px;
    background: #fff;
    -moz-transition: all ease .4s;
    -ms-transition: all ease .4s;
    -webkit-transition: all ease .4s;
    transition: all ease .4s;
    -webkit-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    -moz-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    -ms-box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    box-shadow: 0 1px 4px 0 rgba(0,0,0,0.14);
    border-radius: 50%;
    cursor: pointer;
    z-index: 9;
}
.nbdq-popup .close-popup:hover {
    -webkit-box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);
    -moz-box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);
    -ms-box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);
    box-shadow: 0 3px 10px 0 rgba(75,79,84,.3);
}
.nbdq-popup .close-popup:hover svg{
    color: #0c8ea7;
    fill: #0c8ea7;
}
.nbdq-popup .overlay-popup {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%; }
.nbdq-popup .overlay-main {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: white;
    z-index: -1;
    opacity: 0;
    visibility: hidden;
    transition: all .4s; }
.nbdq-popup .overlay-main.active {
    z-index: 99;
    opacity: 1;
    visibility: visible;
    transition: unset; }
.nbdq-add-a-quote {
    margin-top: 20px;
}
.select2-container--open {
    z-index: 99999999;
}
span.nbdq_error {
    font-size: 11px;
    color: #a00;
}
.nbdq-disabled, .nbdq-disabled2 {
    pointer-events: none;
    opacity: 0.3;
}
.form-row label.radio {
    display: inline-block;
    margin-right: 10px;
}
.nbd-alert-popup .main-popup{
    top: 50%;
    position: absolute;
    left: 50%;
    max-width: 500px;
    transform: translate(calc(-50%), calc(-50%)) scale(0.8) !important;
    margin: 0;
    padding: 0;  
    box-shadow: 0 0 62px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}
.nbd-alert-popup.nb-show .main-popup {
    transform: translate(calc(-50%), calc(-50%)) scale(1) !important;
}
.nbd-alert-popup .main-popup .nbdq-alert-action a:nth-child(2){
    float: right;
}
.nbd-alert-popup .main-popup .nbdq-alert-action:after {
    content: '';
    display: block;
    clear: both;
}
.nbd-alert-popup {
    background: transparent;
}
.nbd-alert-popup .main-popup h3.nbdq-head {
    border-top: 4px solid #404762;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
@media screen and (max-width: 768px){
    .nbd-alert-popup .main-popup,
    #nbdq-form-popup .main-popup {
        width: calc(100% - 30px);
    }
}