/* BASICS */

.CodeMirror {
    /* Set height, width, borders, and global font properties here */
    font-family: 'Consolas', 'Monaco', monospace;
    height: 100%;
    min-height: 60px;
    border: 1px solid #ddd;
    background: #f9f9f9;
    max-width: 95%;
    width: 100%;
    margin-right: 0;
}

.CodeMirror div {
    margin-right: 0 !important;
}

.CodeMirror-scroll {
    /* Set scrolling behaviour here */
    overflow: auto;
}

/* PADDING */

.CodeMirror-lines {
    padding: 6px 6px; /* Vertical padding around content */
}
.CodeMirror pre {
    padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler {
    background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */

.CodeMirror-gutters {
    border-right: 1px solid #ddd;
    background-color: #f7f7f7;
    min-width: 34px;
}
.CodeMirror-gutters .CodeMirror-gutter {
    min-width: 20px !important;
}
.CodeMirror-linenumbers {}
.CodeMirror-linenumber {
    padding: 0;
    min-width: 1em;
    text-align: right;
    color: #999;
}

/* CURSOR */

.CodeMirror div.CodeMirror-cursor {
    border-left: 1px solid black;
}
/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
    border-left: 1px solid silver;
}
.CodeMirror.cm-keymap-fat-cursor div.CodeMirror-cursor {
    width: auto;
    border: 0;
    background: transparent;
    background: rgba(0, 200, 0, .4);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#6600c800, endColorstr=#4c00c800);
}
/* Kludge to turn off filter in ie9+, which also accepts rgba */
.CodeMirror.cm-keymap-fat-cursor div.CodeMirror-cursor:not(#nonsense_id) {
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}
/* Can style cursor different in overwrite (non-insert) mode */
.CodeMirror div.CodeMirror-cursor.CodeMirror-overwrite {}

/* DEFAULT THEME */

.cm-s-default .cm-keyword {color: #999;}
.cm-s-default .cm-atom {color: #219;}
.cm-s-default .cm-number {color: #099;}
.cm-s-default .cm-def {color: #555;}
.cm-s-default .cm-variable {color: black;}
.cm-s-default .cm-variable-2 {color: #099;}
.cm-s-default .cm-variable-3 {color: #085;}
.cm-s-default .cm-property {color: #333;}
.cm-s-default .cm-operator {color: black;}
.cm-s-default .cm-comment {color: #998;}
.cm-s-default .cm-string {color: #d14;}
.cm-s-default .cm-string-2 {color: #333;}
.cm-s-default .cm-meta {color: #333;}
.cm-s-default .cm-error {color: #f00; background: #ddd;}
.cm-s-default .cm-qualifier {color: #458;}
.cm-s-default .cm-builtin {color: #900;}
.cm-s-default .cm-bracket {color: #997;}
.cm-s-default .cm-tag {color: #333;}
.cm-s-default .cm-attribute {color: #00c;}
.cm-s-default .cm-header {color: blue;}
.cm-s-default .cm-quote {color: #090;}
.cm-s-default .cm-hr {color: #999;}
.cm-s-default .cm-link {color: #00c;}

.cm-negative {color: #d44;}
.cm-positive {color: #292;}
.cm-header, .cm-strong {font-weight: bold;}
.cm-em {font-style: italic;}
.cm-emstrong {font-style: italic; font-weight: bold;}
.cm-link {text-decoration: underline;}

.cm-invalidchar {color: #f00;}

div.CodeMirror span.CodeMirror-matchingbracket {color: #0f0;}
div.CodeMirror span.CodeMirror-nonmatchingbracket {color: #f22;}

/* STOP */

/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */

.CodeMirror {
    line-height: 1.4;
    position: relative;
    overflow: hidden;
}

.CodeMirror-scroll {
    /* 30px is the magic margin used to hide the element's real scrollbars */
    /* See overflow: hidden in .CodeMirror, and the paddings in .CodeMirror-sizer */
    margin-bottom: -30px; margin-right: -30px;
    padding-bottom: 30px; padding-right: 30px;
    height: 100%;
    outline: none; /* Prevent dragging from highlighting the element */
    position: relative;
}
.CodeMirror-sizer {
    position: relative;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actuall scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler {
    position: absolute;
    z-index: 6;
    display: none;
}
.CodeMirror-vscrollbar {
    right: 0; top: 0;
    overflow-x: hidden;
    overflow-y: scroll;
}
.CodeMirror-hscrollbar {
    bottom: 0; left: 0;
    overflow-y: hidden;
    overflow-x: scroll;
}
.CodeMirror-scrollbar-filler {
    right: 0; bottom: 0;
    z-index: 6;
}

.CodeMirror-gutters {
    position: absolute; left: 0; top: 0;
    height: 100%;
    padding-bottom: 30px;
    z-index: 3;
}
.CodeMirror-gutter {
    height: 100%;
    display: inline-block;
    /* Hack to make IE7 behave */
    *zoom:1;
    *display:inline;
}
.CodeMirror-gutter-elt {
    position: absolute;
    cursor: default;
    z-index: 4;
}

.CodeMirror-lines {
    cursor: text;
}
.CodeMirror pre {
    /* Reset some styles that the rest of the page might have set */
    -moz-border-radius: 0; -webkit-border-radius: 0; -o-border-radius: 0; border-radius: 0;
    border-width: 0;
    background: transparent;
    font-family: inherit;
    font-size: inherit;
    margin: 0;
    white-space: pre;
    word-wrap: normal;
    line-height: inherit;
    color: inherit;
    z-index: 2;
    position: relative;
    overflow: visible;
}
.CodeMirror-wrap pre {
    word-wrap: break-word;
    white-space: pre-wrap;
    word-break: normal;
}
.CodeMirror-linebackground {
    position: absolute;
    left: 0; right: 0; top: 0; bottom: 0;
    z-index: 0;
}

.CodeMirror-linewidget {
    position: relative;
    z-index: 2;
    overflow: auto;
}

.CodeMirror-widget {
    display: inline-block;
}

.CodeMirror-wrap .CodeMirror-scroll {
    overflow-x: hidden;
}

.CodeMirror-measure {
    position: absolute;
    width: 100%; height: 0px;
    overflow: hidden;
    visibility: hidden;
}
.CodeMirror-measure pre { position: static; }

.CodeMirror div.CodeMirror-cursor {
    position: absolute;
    visibility: hidden;
    border-right: none;
    width: 0;
}
.CodeMirror-focused div.CodeMirror-cursor {
    visibility: visible;
}

.CodeMirror-selected { background: rgba(65,131,196,0.4); }
.CodeMirror-focused .CodeMirror-selected { background: rgba(65,131,196,0.4); }

.cm-searching {
    background: #ffa;
    background: rgba(255, 255, 0, .4);
}

/* IE7 hack to prevent it from returning funny offsetTops on the spans */
.CodeMirror span { *vertical-align: text-bottom; }

@media print {
    /* Hide the cursor when printing */
    .CodeMirror div.CodeMirror-cursor {
        visibility: hidden;
    }
}
.topArrow{    background: url("../images/arrow-up.png") no-repeat;
    border: medium none;
    float: right;
    height: 40px;
    text-indent: -50px;
    border-radius:3px;
    width: 40px;}
.topArrow:hover{ cursor: pointer; background-position: 0px -40px; }
